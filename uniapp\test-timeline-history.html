<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间线历史记录测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #fff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            padding: 20px;
            background: #fff;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        
        .content {
            padding: 20px;
        }
        
        /* 时间线容器样式 */
        .timeline-container {
            display: flex;
            flex-direction: column;
            gap: 0;
        }

        .timeline-day-group {
            display: flex;
            margin-bottom: 40px;
            position: relative;
        }

        /* 时间线日期部分 */
        .timeline-date-section {
            position: relative;
            width: 80px;
            flex-shrink: 0;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            padding-top: 10px;
        }

        .timeline-line {
            position: absolute;
            left: 50%;
            top: 40px;
            bottom: -40px;
            width: 2px;
            background: linear-gradient(180deg, #e91e63 0%, #9c27b0 100%);
            transform: translateX(-50%);
            z-index: 1;
        }

        .timeline-line.first-line {
            top: 40px;
        }

        .timeline-date-badge {
            position: relative;
            background: linear-gradient(135deg, #e91e63 0%, #9c27b0 100%);
            border-radius: 20px;
            padding: 8px 12px;
            box-shadow: 0 4px 12px rgba(233, 30, 99, 0.3);
            z-index: 2;
            min-width: 60px;
            text-align: center;
        }

        .date-text {
            color: #fff;
            font-size: 12px;
            font-weight: 600;
            white-space: nowrap;
        }

        /* 时间线卡片部分 */
        .timeline-cards-section {
            flex: 1;
            margin-left: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: 15px;
            align-items: start;
        }

        /* 历史卡片样式 */
        .history-card-wrapper {
            width: 100%;
        }

        .history-card {
            position: relative;
            width: 100%;
            height: 180px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            cursor: pointer;
            background: #fff;
        }

        .history-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .card-background-icon {
            width: 100%;
            height: 100%;
            object-fit: cover;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
        }

        .consumption-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            z-index: 3;
        }

        .card-title-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            padding: 20px 12px 12px;
            z-index: 2;
        }

        .title-text {
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .status-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            z-index: 3;
        }

        .status-success {
            background: #4caf50;
            color: #fff;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .timeline-container {
                padding: 0 10px;
            }

            .timeline-date-section {
                width: 60px;
            }

            .timeline-date-badge {
                padding: 6px 8px;
                min-width: 50px;
            }

            .date-text {
                font-size: 11px;
            }

            .timeline-cards-section {
                margin-left: 15px;
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                gap: 12px;
            }

            .timeline-day-group {
                margin-bottom: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">往档 - 创作记录（时间线版本）</div>
        </div>
        
        <div class="content">
            <div class="timeline-container" id="timelineContainer">
                <!-- 时间线内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const mockTasks = [
            {
                id: 1,
                taskType: '智能体',
                taskTypeDetail: 'AI写作助手',
                status: '已完成',
                createTime: '2024-01-15T10:30:00Z',
                consumption: { tokens: 150 },
                avatar: 'https://via.placeholder.com/150x150/667eea/ffffff?text=AI'
            },
            {
                id: 2,
                taskType: '工作流',
                taskTypeDetail: '图片生成流程',
                status: '已完成',
                createTime: '2024-01-15T14:20:00Z',
                consumption: { tokens: 200 },
                avatar: 'https://via.placeholder.com/150x150/e91e63/ffffff?text=IMG'
            },
            {
                id: 3,
                taskType: '智能体',
                taskTypeDetail: '代码助手',
                status: '已完成',
                createTime: '2024-01-14T09:15:00Z',
                consumption: { tokens: 300 },
                avatar: 'https://via.placeholder.com/150x150/9c27b0/ffffff?text=CODE'
            },
            {
                id: 4,
                taskType: '工作流',
                taskTypeDetail: '视频处理',
                status: '已完成',
                createTime: '2024-01-14T16:45:00Z',
                consumption: { tokens: 500 },
                avatar: 'https://via.placeholder.com/150x150/ff9800/ffffff?text=VID'
            },
            {
                id: 5,
                taskType: '智能体',
                taskTypeDetail: '翻译助手',
                status: '已完成',
                createTime: '2024-01-13T11:30:00Z',
                consumption: { tokens: 120 },
                avatar: 'https://via.placeholder.com/150x150/4caf50/ffffff?text=TRANS'
            }
        ];

        // 按日期分组任务
        function groupTasksByDate(tasks) {
            const groups = {};
            
            tasks.forEach(task => {
                const date = getDateKey(task.createTime);
                if (!groups[date]) {
                    groups[date] = {
                        date: date,
                        dateLabel: getDateLabel(task.createTime),
                        tasks: []
                    };
                }
                groups[date].tasks.push(task);
            });

            // 转换为数组并按日期排序（最新的在前）
            return Object.values(groups).sort((a, b) => {
                return new Date(b.date) - new Date(a.date);
            });
        }

        // 获取日期键
        function getDateKey(dateString) {
            const date = new Date(dateString);
            return date.toISOString().split('T')[0];
        }

        // 获取日期标签
        function getDateLabel(dateString) {
            const date = new Date(dateString);
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);

            if (isSameDay(date, today)) {
                return '今天';
            }
            
            if (isSameDay(date, yesterday)) {
                return '昨天';
            }

            const weekStart = new Date(today);
            weekStart.setDate(today.getDate() - today.getDay());
            if (date >= weekStart) {
                const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                return weekdays[date.getDay()];
            }

            return `${date.getMonth() + 1}月${date.getDate()}日`;
        }

        // 判断是否同一天
        function isSameDay(date1, date2) {
            return date1.getFullYear() === date2.getFullYear() &&
                   date1.getMonth() === date2.getMonth() &&
                   date1.getDate() === date2.getDate();
        }

        // 渲染时间线
        function renderTimeline() {
            const container = document.getElementById('timelineContainer');
            const groupedTasks = groupTasksByDate(mockTasks);
            
            container.innerHTML = groupedTasks.map((dayGroup, dayIndex) => `
                <div class="timeline-day-group">
                    <div class="timeline-date-section">
                        <div class="timeline-line ${dayIndex === 0 ? 'first-line' : ''}"></div>
                        <div class="timeline-date-badge">
                            <span class="date-text">${dayGroup.dateLabel}</span>
                        </div>
                    </div>
                    
                    <div class="timeline-cards-section">
                        ${dayGroup.tasks.map(task => `
                            <div class="history-card-wrapper">
                                <div class="history-card" onclick="showTaskDetail(${task.id})">
                                    <img src="${task.avatar}" class="card-background-icon" alt="${task.taskTypeDetail}">
                                    
                                    <div class="consumption-badge">
                                        <span>${task.consumption.tokens}点</span>
                                    </div>
                                    
                                    <div class="card-title-overlay">
                                        <span class="title-text">${task.taskTypeDetail}</span>
                                    </div>
                                    
                                    <div class="status-badge status-success">
                                        <span>已完成</span>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');
        }

        // 显示任务详情
        function showTaskDetail(taskId) {
            const task = mockTasks.find(t => t.id === taskId);
            if (task) {
                alert(`任务详情：\n类型：${task.taskType}\n名称：${task.taskTypeDetail}\n状态：${task.status}\n消耗：${task.consumption.tokens}点`);
            }
        }

        // 页面加载完成后渲染时间线
        document.addEventListener('DOMContentLoaded', renderTimeline);
    </script>
</body>
</html>
