<template>
	<view class="container">
		<view class="header">
			<text class="title">往档 - 创作记录</text>
		</view>

		<view class="content">

			<!-- 任务类型筛选标签 -->
			<view class="type-filter">
				<view
					class="filter-item"
					:class="{ active: activeFilter === 'all' }"
					@click="switchFilter('all')"
				>
					<text class="filter-text">全部</text>
				</view>
				<view
					class="filter-item"
					:class="{ active: activeFilter === 'agent' }"
					@click="switchFilter('agent')"
				>
					<text class="filter-text">智能体</text>
				</view>
				<view
					class="filter-item"
					:class="{ active: activeFilter === 'workflow' }"
					@click="switchFilter('workflow')"
				>
					<text class="filter-text">工作流</text>
				</view>
			</view>

			<!-- 任务历史列表 -->
			<view class="history-grid">
				<!-- 未登录提示 -->
				<view v-if="!isLoggedIn" class="login-prompt">
					<view class="prompt-icon">🔒</view>
					<text class="prompt-title">请先登录</text>
					<text class="prompt-desc">登录后即可查看您的创作记录</text>
					<button class="login-btn" @click="showLoginModal">立即登录</button>
				</view>

				<view v-else-if="isLoadingTasks" class="loading-message">
					<text>正在加载任务...</text>
				</view>

				<view v-else-if="taskRecords.length === 0" class="empty-state">
					<view class="empty-icon">🎨</view>
					<view class="empty-text">暂无创作记录</view>
					<view class="empty-hint">开始您的创作之旅吧</view>
					<view class="create-button" @click="goToCreate">
						<text class="create-button-text">前往创作</text>
					</view>
				</view>

				<view v-else class="timeline-container">
					<!-- 连续的垂直时间线 -->
					<view class="timeline-line-continuous"></view>

					<!-- 时间线内容 -->
					<view v-for="(dayGroup, dayIndex) in groupedTaskRecords" :key="dayGroup.date" class="timeline-day-group">
						<!-- 时间标签在左上角 -->
						<view class="timeline-date-badge">
							<text class="date-text">{{ dayGroup.dateLabel }}</text>
						</view>

						<view class="timeline-content-row">
							<!-- 时间线圆点 -->
							<view class="timeline-dot-container">
								<view class="timeline-dot"></view>
							</view>

							<!-- 卡片网格 -->
							<view class="timeline-cards-grid">
								<view v-for="(item, index) in dayGroup.tasks" :key="item.id" class="history-card-wrapper">
									<!-- 卡片主体 -->
									<view class="history-card" @click="showContentDetail(item)">
										<!-- 背景图标铺满整个卡片 -->
										<image
											:src="getAgentAvatar(item)"
											class="card-background-icon"
											mode="aspectFill"
											@error="onAvatarError"
										/>

										<!-- 左上角消耗点数标签 -->
										<view v-if="getConsumptionAmount(item) > 0" class="consumption-badge">
											<text class="consumption-text">{{ getConsumptionAmount(item) }}点</text>
										</view>

										<!-- 底部居中标题 -->
										<view class="card-title-overlay">
											<text class="title-text">{{ getAgentTitle(item) }}</text>
										</view>

										<!-- 右上角状态标签 -->
										<view class="status-badge" :class="getStatusClass(item.status)">
											<text class="status-text">{{ getStatusText(item.status) }}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 加载更多 -->
				<view v-if="taskPagination.hasMore && !isLoadingTasks" class="load-more" @click="loadMoreTasks">
					<text class="load-more-text">加载更多</text>
				</view>
			</view>
		</view>

		<!-- 全新设计的内容详情弹窗 -->
		<view v-if="showDetailModal" class="new-modal-overlay" @click="closeDetailModal">
			<view class="new-modal-container" @click.stop>
				<!-- 弹窗头部 -->
				<view class="new-modal-header">
					<view class="header-content">
						<text class="modal-title">{{ currentItem.agent_name || getAgentTitle(currentItem) }}</text>
						<text class="modal-subtitle">{{ formatDate(currentItem.created_at) }}</text>
					</view>
					<button class="close-button" @click="closeDetailModal">
						<text class="close-icon">✕</text>
					</button>
				</view>

				<!-- 弹窗主体 -->
				<view class="new-modal-body">

					<!-- 输入内容 -->
					<view class="info-card">
						<view class="info-header">
							<text class="info-title">📝 输入内容</text>
						</view>
						<view class="info-content">
							<text class="input-text" selectable="true">{{ currentItem.input || currentItem.prompt || '无输入内容' }}</text>
						</view>
					</view>

					<!-- 输出内容 -->
					<view class="info-card" v-if="currentItem.output">
						<view class="info-header">
							<text class="info-title">🎯 生成结果</text>
							<text class="result-count" v-if="parseOutputContent(currentItem.output).length > 1">
								{{ parseOutputContent(currentItem.output).length }} 项
							</text>
						</view>
						<view class="info-content">
							<view v-for="(content, contentIndex) in parseOutputContent(currentItem.output)" :key="contentIndex" class="result-item">
									<!-- 文本内容 -->
									<view v-if="content.type === 'text'" class="text-result">
										<text class="text-content" selectable="true">{{ content.text }}</text>
									</view>

									<!-- 图片内容 -->
									<view v-else-if="content.type === 'image'" class="media-result">
										<view class="media-header">
											<view class="media-info">
												<text class="media-title">{{ content.title || `图片 ${content.index}` }}</text>
												<view class="media-badge image-badge">IMG</view>
											</view>
										</view>
										<view class="media-preview">
											<image :src="content.url" class="media-image" mode="aspectFit" @click="previewImage(content.url)" @error="onImageError"></image>
											<view class="error-overlay" v-if="imageLoadError[contentIndex]">
												<text class="error-text">图片加载失败</text>
											</view>
										</view>
										<view class="media-actions">
											<button @click="previewImage(content.url)" class="action-btn primary-btn">
												<text class="btn-icon">👁</text>
												<text class="btn-text">预览</text>
											</button>
											<button @click="copyToClipboard(content.originalUrl || content.url, '图片链接')" class="action-btn secondary-btn">
												<text class="btn-icon">📋</text>
												<text class="btn-text">复制</text>
											</button>
											<button @click="saveImage(content.url)" class="action-btn secondary-btn">
												<text class="btn-icon">💾</text>
												<text class="btn-text">保存</text>
											</button>
										</view>
									</view>

									<!-- 视频内容 -->
									<view v-else-if="content.type === 'video'" class="media-result">
										<view class="media-header">
											<view class="media-info">
												<text class="media-title">{{ content.title || `视频 ${content.index}` }}</text>
												<view class="media-badge video-badge">VID</view>
											</view>
										</view>
										<view class="media-preview">
											<video :src="content.url" class="media-video" controls @error="onVideoError"></video>
										</view>
										<view class="media-actions">
											<button @click="copyToClipboard(content.originalUrl || content.url, '视频链接')" class="action-btn secondary-btn">
												<text class="btn-icon">📋</text>
												<text class="btn-text">复制</text>
											</button>
											<button @click="saveVideo(content.url)" class="action-btn secondary-btn">
												<text class="btn-icon">💾</text>
												<text class="btn-text">保存</text>
											</button>
										</view>
									</view>

									<!-- 链接内容 - 全新设计 -->
									<view v-else-if="content.type === 'link'" class="link-result">
										<view class="link-header">
											<view class="link-info">
												<text class="link-title">{{ content.title || `链接 ${content.index}` }}</text>
												<view class="link-badge">LINK</view>
											</view>
										</view>
										<view class="link-content">
											<view class="link-url-container">
												<text class="link-url" selectable="true">{{ content.url }}</text>
											</view>
										</view>
										<view class="link-actions">
											<button @click="openLink(content.url)" class="action-btn primary-btn">
												<text class="btn-icon">🔗</text>
												<text class="btn-text">打开链接</text>
											</button>
											<button @click="copyToClipboard(content.url, '链接')" class="action-btn secondary-btn">
												<text class="btn-icon">📋</text>
												<text class="btn-text">复制链接</text>
											</button>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 魔法导航栏 -->
		<MagicNavigation
			:current="3"
			:items="navItems"
			@change="onNavChange"
		/>

		<!-- 智能登录弹窗 -->
		<smart-login-modal
			:visible="showLoginModalFlag"
			@close="hideLoginModal"
			@login-success="handleLoginSuccess"
		></smart-login-modal>

		<!-- 任务详情弹窗 -->
		<view v-if="showTaskDetail" class="task-detail-modal" @click="closeTaskDetail">
			<view class="modal-content" @click.stop @click="hideImageActions">
				<view class="modal-header">
					<view class="header-info">
						<text class="task-name">{{ getAgentTitle(selectedTask) }}</text>
						<text class="task-time">{{ formatDate(selectedTask.createTime) }}</text>
					</view>
					<view class="close-btn" @click="closeTaskDetail">
						<text class="close-icon">×</text>
					</view>
				</view>

				<view class="modal-body">

					<!-- 输入内容 -->
					<view class="info-card">
						<view class="card-header">
							<text class="card-title">输入内容</text>
						</view>
						<view class="card-content">
							<text class="input-text">{{ selectedTask.input || selectedTask.prompt || '无输入内容' }}</text>
						</view>
					</view>

					<!-- 生成结果 -->
					<view class="info-card">
						<view class="card-header">
							<text class="card-title">🎯 生成结果</text>
						</view>
						<view class="card-content">
							<!-- 如果包含图片链接，显示图片 -->
							<view v-if="taskLinks.length > 0" class="result-images">
								<view class="result-image-grid">
									<view class="result-image-item" v-for="(link, index) in taskLinks" :key="index">
										<image
											:src="link"
											class="result-image"
											mode="aspectFit"
											@error="onImageError"
											@load="onImageLoad"
										/>
										<!-- 操作按钮覆盖层 -->
										<view class="image-overlay">
											<view class="image-actions">
												<button class="action-btn preview-btn" @click="previewImageWithToast(link, index)">
													<text class="btn-icon">👁️</text>
												</button>
												<button class="action-btn copy-btn" @click="copyImageWithToast(link)">
													<text class="btn-icon">📋</text>
												</button>
												<button class="action-btn download-btn" @click="saveImageWithToast(link, index)">
													<text class="btn-icon">💾</text>
												</button>
											</view>
										</view>
									</view>
								</view>
							</view>

							<!-- 如果没有图片链接，显示文本 -->
							<view v-else>
								<text class="output-text">{{ formatJsonOutput(selectedTask.output) }}</text>
							</view>
						</view>
					</view>


				</view>
			</view>
		</view>
</template>

<script>
import MagicNavigation from '@/components/MagicNavigation.vue'
import SmartLoginModal from '@/components/smart-login-modal.vue'
import { userStore } from '@/api/members.js'
import tasksApi from '@/api/tasks.js'
import { IMAGE_BASE_URL } from '@/config/index.js'

export default {
	name: 'HistoryIndex',
	components: {
		MagicNavigation,
		SmartLoginModal
	},
	data() {
		return {
			activeFilter: 'all', // 当前选中的任务类型筛选
			taskRecords: [], // 任务记录列表
			isLoadingTasks: false, // 任务加载状态
			taskPagination: { // 任务分页
				page: 1,
				pageSize: 20,
				total: 0,
				hasMore: true
			},
			taskStats: { // 任务统计信息
				totalTasks: 0,
				completedTasks: 0,
				weeklyTokens: 0,
				totalTokens: 0,
				remainingTokens: 0
			},
			showDetailModal: false, // 详情弹窗显示状态
			currentItem: {}, // 当前查看的项目
			creationHistory: [], // 创作历史记录
			usageHistory: [], // 使用历史记录
			navItems: [
				{ text: '初页', icon: 'icon-home', path: '/pages/index/index' },
				{ text: '同创', icon: 'icon-robot', path: '/pages/create/index' },
				{ text: '会享', icon: 'icon-message', path: '/pages/chat/index' },
				{ text: '往档', icon: 'icon-history', path: '/pages/history/index' },
				{ text: '吾界', icon: 'icon-user', path: '/pages/profile/index' }
			],
			// 登录相关状态
			showLoginModalFlag: false, // 控制登录弹窗显示
			imageLoadError: {}, // 记录图片加载错误状态
			isLoggedIn: false, // 用户登录状态
			// 弹窗相关状态
			showTaskDetail: false, // 任务详情弹窗显示状态
			selectedTask: {}, // 当前选中的任务
			taskLinks: [], // 任务中的链接列表
			activeImageIndex: -1, // 当前激活的图片索引
			// 任务统计信息
			taskStats: {
				totalTasks: 0,
				completedTasks: 0,
				weeklyTokens: 0,
				totalTokens: 0,
				remainingTokens: 0
			}
		}
	},
	computed: {
		// 按日期分组的任务记录
		groupedTaskRecords() {
			if (!this.taskRecords || this.taskRecords.length === 0) {
				return [];
			}

			// 按日期分组
			const groups = {};
			this.taskRecords.forEach(task => {
				const date = this.getDateKey(task.createTime);
				if (!groups[date]) {
					groups[date] = {
						date: date,
						dateLabel: this.getDateLabel(task.createTime),
						tasks: []
					};
				}
				groups[date].tasks.push(task);
			});

			// 转换为数组并按日期排序（最新的在前）
			return Object.values(groups).sort((a, b) => {
				return new Date(b.date) - new Date(a.date);
			});
		}
	},
	onLoad() {
		try {
			// 检查登录状态
			this.checkLoginStatus();
			// 加载任务历史
			this.loadTaskHistory();
		} catch (error) {
			// 静默处理错误
		}
	},
	onShow() {
		try {
			// 页面显示时重新检查登录状态
			this.checkLoginStatus();
			// 刷新任务历史
			this.refreshTaskHistory();
		} catch (error) {
			// 静默处理错误
		}
	},
	methods: {
		// 跳转到首页创作
		goToCreate() {
			uni.navigateTo({
				url: '/pages/index/index'
			});
		},

		onNavChange(event) {
			if (event.item.path && event.item.path !== '/pages/history/index') {
				uni.navigateTo({
					url: event.item.path
				})
			}
		},
		// 切换任务类型筛选
		switchFilter(filter) {
			try {
				this.activeFilter = filter;
				// 重新加载任务历史
				this.refreshTaskHistory();
			} catch (error) {
				// 静默处理错误
			}
		},

		// 刷新任务历史
		refreshTaskHistory() {
			this.taskPagination.page = 1;
			this.taskPagination.hasMore = true;
			this.taskRecords = [];
			this.loadTaskHistory();
		},



		// 检查登录状态
		checkLoginStatus() {
			this.isLoggedIn = userStore.isLoggedIn();
		},

		// 显示登录弹窗
		showLoginModal() {
			this.showLoginModalFlag = true;
		},

		// 隐藏登录弹窗
		hideLoginModal() {
			this.showLoginModalFlag = false;
		},

		// 处理登录成功
		handleLoginSuccess(memberData) {
			console.log('登录成功，用户数据:', memberData);
			// 刷新登录状态
			this.checkLoginStatus();
			// 隐藏登录弹窗
			this.hideLoginModal();
			// 显示成功提示
			uni.showToast({
				title: '登录成功',
				icon: 'success'
			});
			// 延迟一下再重新加载任务历史，确保用户信息已保存
			setTimeout(() => {
				this.refreshTaskHistory();
			}, 500);
		},

		// 加载任务历史
		async loadTaskHistory() {
			if (this.isLoadingTasks || !this.taskPagination.hasMore) {
				return;
			}

			// 检查登录状态
			if (!this.isLoggedIn) {
				console.log('用户未登录，无法加载任务历史');
				return;
			}

			try {
				this.isLoadingTasks = true;

				// 获取当前用户信息
				const currentUser = userStore.getCurrentUser();
				console.log('当前用户信息:', currentUser);
				if (!currentUser || !currentUser.id) {
					console.log('用户未登录或用户信息不完整');
					this.isLoadingTasks = false;
					return;
				}

				// 构建请求参数
				const params = {
					page: this.taskPagination.page,
					pageSize: this.taskPagination.pageSize,
					userId: currentUser.id // 添加用户ID，只获取当前用户的任务
				};
				console.log('任务请求参数:', params);

				// 根据任务类型筛选添加参数
				if (this.activeFilter === 'agent') {
					params.type = 'agent'; // 智能体
				} else if (this.activeFilter === 'workflow') {
					params.type = 'workflow'; // 工作流
				}
				// 'all' 不添加type参数，获取所有类型

				console.log('加载任务历史，参数:', params);
				const response = await tasksApi.getTasks(params);

				if (response.success && response.data) {
					// 兼容不同的数据结构
					let tasks = response.data.tasks || response.data || [];
					let pagination = response.data.pagination || { total: 0 };
					let stats = response.data.stats || {};

					// 格式化任务记录
					const formattedRecords = tasksApi.formatTaskRecords(tasks);

					// 如果是第一页，替换数据；否则追加数据
					if (this.taskPagination.page === 1) {
						this.taskRecords = formattedRecords;
					} else {
						this.taskRecords.push(...formattedRecords);
					}

					// 更新分页信息
					this.taskPagination = {
						...this.taskPagination,
						total: pagination.total,
						hasMore: this.taskPagination.page * this.taskPagination.pageSize < pagination.total
					};

					// 更新统计信息
					if (stats) {
						this.taskStats = stats;
					}


				}
			} catch (error) {
				console.error('加载任务历史失败:', error);
				uni.showToast({
					title: '加载失败，请检查网络',
					icon: 'none'
				});
			} finally {
				this.isLoadingTasks = false;
			}
		},

		// 加载更多任务历史
		async loadMoreTasks() {
			if (this.taskPagination.hasMore && !this.isLoadingTasks) {
				this.taskPagination.page++;
				await this.loadTaskHistory();
			}
		},

		// 查看任务详情
		viewTaskDetail(taskItem) {
			try {

				// 构建详情内容
				let content = `智能体：${this.getAgentTitle(taskItem)}\n状态：${taskItem.status || '未知'}`;

				if (taskItem.output) {
					content += `\n\n输出：${taskItem.output}`;
				}



				// 显示任务详情弹窗
				uni.showModal({
					title: '任务详情',
					content: content,
					showCancel: false,
					confirmText: '关闭'
				});
			} catch (error) {
				// 静默处理错误
			}
		},

		// 显示内容详情弹窗
		showContentDetail(item) {
			this.selectedTask = item;
			this.taskLinks = this.extractLinksFromTask(item);
			this.showTaskDetail = true;
		},

		// 关闭详情弹窗
		closeTaskDetail() {
			this.showTaskDetail = false;
			this.selectedTask = {};
			this.taskLinks = [];
		},

		// 从任务中提取链接
		extractLinksFromTask(task) {
			const links = [];
			if (task.output) {
				try {
					// 如果是JSON字符串，解析它
					let content = task.output;

					if (typeof content === 'string') {
						try {
							content = JSON.parse(content);
						} catch (e) {
							// 如果不是JSON，直接使用字符串内容
						}
					}

					// 提取链接
					if (typeof content === 'object' && content !== null) {
						for (const [key, value] of Object.entries(content)) {
							if (typeof value === 'string' && this.isValidUrl(value)) {
								links.push(value);
							}
						}
					} else if (typeof content === 'string') {
						// 改进的URL提取逻辑
						// 1. 先尝试匹配完整的URL（直到遇到空白字符、引号或特殊字符）
						const urlRegex = /https?:\/\/[^\s"'<>\[\]{}|\\^`]+/g;
						let matches = content.match(urlRegex);

						if (matches) {
							// 清理每个URL，移除末尾可能的标点符号
							matches = matches.map(url => {
								// 移除末尾的标点符号（但保留URL中正常的字符）
								return url.replace(/[.,;:!?)\]}]+$/, '');
							}).filter(url => {
								// 确保清理后的URL仍然有效
								return this.isValidUrl(url);
							});

							links.push(...matches);
						}

						// 2. 如果没有找到链接，尝试更宽松的匹配
						if (links.length === 0) {
							const looseRegex = /https?:\/\/[^\s]+/g;
							const looseMatches = content.match(looseRegex);
							if (looseMatches) {
								looseMatches.forEach(url => {
									// 尝试清理URL
									const cleanUrl = url.replace(/[.,;:!?)\]}"']+$/, '');
									if (this.isValidUrl(cleanUrl)) {
										links.push(cleanUrl);
									}
								});
							}
						}
					}
				} catch (error) {
					console.error('提取链接时出错:', error);
				}
			}

			// 去重
			return [...new Set(links)];
		},

		// 验证是否为有效URL
		isValidUrl(string) {
			try {
				const url = new URL(string);
				return url.protocol === 'http:' || url.protocol === 'https:';
			} catch (_) {
				return false;
			}
		},

		// 截断URL显示
		truncateUrl(url) {
			if (!url) return '';

			// 如果URL长度小于60，直接返回
			if (url.length <= 60) {
				return url;
			}

			// 提取域名部分
			try {
				const urlObj = new URL(url);
				const domain = urlObj.hostname;
				const path = urlObj.pathname + urlObj.search;

				// 如果路径很短，显示完整URL的前50个字符
				if (path.length <= 20) {
					return url.substring(0, 50) + '...';
				}

				// 显示域名 + 路径开头 + ... + 路径结尾
				const pathStart = path.substring(0, 15);
				const pathEnd = path.substring(path.length - 10);
				return `${domain}${pathStart}...${pathEnd}`;
			} catch (e) {
				// 如果URL解析失败，直接截断
				return url.substring(0, 50) + '...';
			}
		},

		// 获取输入预览文本
		getInputPreview(input) {
			if (!input) return '创作内容';
			return input.length > 30 ? input.substring(0, 30) + '...' : input;
		},

		// 复制到剪贴板
		copyToClipboard(text, type = '内容') {
			uni.setClipboardData({
				data: text,
				success: () => {
					uni.showToast({
						title: `${type}已复制`,
						icon: 'success'
					});
				},
				fail: () => {
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					});
				}
			});
		},

		// 获取状态样式类
		getStatusClass(status) {
			const statusMap = {
				'completed': 'status-success',
				'success': 'status-success',
				'已完成': 'status-success',
				'完成': 'status-success',
				'failed': 'status-error',
				'error': 'status-error',
				'失败': 'status-error',
				'错误': 'status-error'
			};
			return statusMap[status] || 'status-default';
		},

		// 获取状态显示文本
		getStatusText(status) {
			const statusMap = {
				'completed': '已完成',
				'success': '已完成',
				'已完成': '已完成',
				'完成': '已完成',
				'failed': '失败',
				'error': '失败',
				'失败': '失败',
				'错误': '失败'
			};
			return statusMap[status] || '进行中';
		},

		// 获取任务图标
		getTaskIcon(taskType) {
			const iconMap = {
				'智能体': '🤖',
				'工作流': '⚙️',
				'文本助手': '📝',
				'代码助手': '💻',
				'内容生成': '✨'
			};
			return iconMap[taskType] || '📋';
		},



		// 获取智能体头像
		getAgentAvatar(item) {
			try {
				// 直接从数据项中获取 agentAvatar 字段
				if (item.agentAvatar) {
					// 如果是完整URL，直接返回；否则添加IMAGE_BASE_URL前缀
					if (item.agentAvatar.startsWith('http')) {
						return item.agentAvatar;
					} else {
						return IMAGE_BASE_URL + item.agentAvatar;
					}
				}

				// 对于工作流类型，从 taskSource 字段获取图标
				if (item.taskType === '工作流' && item.taskSource) {
					// 如果是完整URL，直接返回；否则添加IMAGE_BASE_URL前缀
					if (item.taskSource.startsWith('http')) {
						return item.taskSource;
					} else {
						return IMAGE_BASE_URL + item.taskSource;
					}
				}

				// 从 consumption 字段中获取智能体头像
				if (item.consumption && typeof item.consumption === 'object') {
					if (item.consumption.agentAvatar) {
						if (item.consumption.agentAvatar.startsWith('http')) {
							return item.consumption.agentAvatar;
						} else {
							return IMAGE_BASE_URL + item.consumption.agentAvatar;
						}
					}
				}

				// 如果 consumption 是字符串，尝试解析
				if (item.consumption && typeof item.consumption === 'string') {
					try {
						const consumptionData = JSON.parse(item.consumption);
						if (consumptionData.agentAvatar) {
							if (consumptionData.agentAvatar.startsWith('http')) {
								return consumptionData.agentAvatar;
							} else {
								return IMAGE_BASE_URL + consumptionData.agentAvatar;
							}
						}
					} catch (parseError) {
						// 静默处理解析错误
					}
				}

				// 根据任务类型返回默认图标
				if (item.taskType === '工作流') {
					return '/static/images/workflow-default.png';
				} else if (item.taskType === '智能体' || item.taskTypeDetail?.includes('智能体')) {
					return '/static/images/znt_avatar.png';
				}

				// 其他类型任务使用默认头像
				return '/static/images/znt_avatar.png';
			} catch (error) {
				// 根据任务类型返回默认图标
				if (item.taskType === '工作流') {
					return '/static/images/workflow-default.png';
				} else {
					return '/static/images/znt_avatar.png';
				}
			}
		},

		// 获取智能体标题
		getAgentTitle(item) {
			try {
				// 优先使用 taskTypeDetail 字段保存的智能体标题
				if (item.taskTypeDetail && item.taskTypeDetail !== 'Coze智能体对话') {
					return item.taskTypeDetail;
				}

				// 从 consumption 字段中获取智能体名称
				if (item.consumption && typeof item.consumption === 'object') {
					if (item.consumption.agentName) {
						return item.consumption.agentName;
					}
				}

				// 如果 consumption 是字符串，尝试解析
				if (item.consumption && typeof item.consumption === 'string') {
					try {
						const consumptionData = JSON.parse(item.consumption);
						if (consumptionData.agentName) {
							return consumptionData.agentName;
						}
					} catch (parseError) {
						// 静默处理解析错误
					}
				}

				// 如果是智能体任务，显示默认名称
				if (item.taskType === '智能体') {
					return 'Coze智能体';
				}

				// 其他类型显示任务类型详情
				return item.taskTypeDetail || item.taskType;
			} catch (error) {
				return item.taskTypeDetail || item.taskType || '未知智能体';
			}
		},

		// 获取智能体ID
		getAgentId(item) {
			try {
				// 从 consumption 字段中获取 botId
				if (item.consumption && typeof item.consumption === 'object') {
					if (item.consumption.botId) {
						return item.consumption.botId;
					}
				}

				// 如果 consumption 是字符串，尝试解析
				if (item.consumption && typeof item.consumption === 'string') {
					try {
						const consumptionData = JSON.parse(item.consumption);
						if (consumptionData.botId) {
							return consumptionData.botId;
						}
					} catch (parseError) {
						// 静默处理解析错误
					}
				}

				// 从 instructions 字段中提取 botId
				if (item.instructions && item.instructions.includes('(') && item.instructions.includes(')')) {
					const match = item.instructions.match(/\(([^)]+)\)/);
					if (match && match[1]) {
						return match[1];
					}
				}

				return null;
			} catch (error) {
				return null;
			}
		},

		// 获取消耗点数
		getConsumptionAmount(item) {
			try {
				// 如果 consumption 是对象，直接获取
				if (item.consumption && typeof item.consumption === 'object') {
					return item.consumption.amount || item.consumption.tokens || item.consumption.consumption || 0;
				}

				// 如果 consumption 是字符串，尝试解析
				if (item.consumption && typeof item.consumption === 'string') {
					try {
						const consumptionData = JSON.parse(item.consumption);
						return consumptionData.amount || consumptionData.tokens || consumptionData.consumption || 0;
					} catch (parseError) {
						// 如果解析失败，尝试直接转换为数字
						const numValue = parseFloat(item.consumption);
						return isNaN(numValue) ? 0 : numValue;
					}
				}

				// 如果是数字类型，直接返回
				if (typeof item.consumption === 'number') {
					return item.consumption;
				}

				return 0;
			} catch (error) {
				console.error('获取消耗点数失败:', error);
				return 0;
			}
		},

		// 头像加载失败处理
		onAvatarError(e) {
			// 可以设置默认头像
			e.target.src = '/static/images/default-avatar.png';
		},

		// 复制内容到剪贴板
		copyContent(content, type) {
			if (!content) {
				uni.showToast({
					title: '内容为空',
					icon: 'none'
				});
				return;
			}

			// 使用uni.setClipboardData复制到剪贴板
			uni.setClipboardData({
				data: content,
				success: () => {
					uni.showToast({
						title: `${type}已复制`,
						icon: 'success'
					});
				},
				fail: () => {
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					});
				}
			});
		},

		// 复制任务信息
		copyTaskInfo(item) {
			try {
				let taskInfo = `任务信息\n`;
				taskInfo += `================\n`;
				taskInfo += `任务类型: ${item.taskType}\n`;

				const agentName = this.getAgentTitle(item);
				if (agentName) {
					taskInfo += `智能体名称: ${agentName}\n`;
				}

				const agentId = this.getAgentId(item);
				if (agentId) {
					taskInfo += `智能体ID: ${agentId}\n`;
				}

				const agentAvatar = this.getAgentAvatar(item);
				if (agentAvatar) {
					taskInfo += `智能体图标: ${agentAvatar}\n`;
				}

				taskInfo += `创建时间: ${item.createTime}\n`;
				taskInfo += `任务状态: ${item.status}\n`;
				taskInfo += `================\n`;

				if (item.output) {
					taskInfo += `输出内容:\n${item.output}\n`;
				}

				this.copyContent(taskInfo, '任务信息');
			} catch (error) {
				uni.showToast({
					title: '复制失败',
					icon: 'none'
				});
			}
		},

		// 格式化时间显示
		formatTime(timeStr) {
			if (!timeStr) return '';

			try {
				const date = new Date(timeStr);
				const now = new Date();
				const diff = now - date;

				// 小于1分钟显示"刚刚"
				if (diff < 60000) {
					return '刚刚';
				}

				// 小于1小时显示"X分钟前"
				if (diff < 3600000) {
					return `${Math.floor(diff / 60000)}分钟前`;
				}

				// 小于24小时显示"X小时前"
				if (diff < 86400000) {
					return `${Math.floor(diff / 3600000)}小时前`;
				}

				// 超过24小时显示具体日期
				return date.toLocaleDateString() + ' ' + date.toLocaleTimeString().slice(0, 5);
			} catch (error) {
				return timeStr;
			}
		},

		// 智能解析输出内容 - 美化版本，优雅处理JSON和普通文本
		parseOutputContent(output) {
			if (!output) return [];

			const contents = [];
			const allLinks = [];

			// 首先尝试解析JSON格式的输出
			try {
				const jsonData = JSON.parse(output);
				if (typeof jsonData === 'object' && jsonData !== null) {
					// 从JSON对象中提取所有链接
					this.extractLinksFromObject(jsonData, allLinks);

					// 如果找到了JSON中的链接，美化显示
					if (allLinks.length > 0) {
						// 添加一个友好的提示文本
						contents.push({
							type: 'text',
							text: `生成了 ${allLinks.length} 个文件：`
						});

						allLinks.forEach((linkUrl, index) => {
							const isImage = this.isImageUrl(linkUrl);
							const isVideo = this.isVideoUrl(linkUrl);

							if (isImage) {
								contents.push({
									type: 'image',
									url: linkUrl,
									originalUrl: linkUrl,
									index: index + 1,
									title: this.getImageTitle(jsonData, linkUrl, index) // 添加标题
								});
							} else if (isVideo) {
								contents.push({
									type: 'video',
									url: linkUrl,
									originalUrl: linkUrl,
									index: index + 1,
									title: this.getVideoTitle(jsonData, linkUrl, index) // 添加标题
								});
							} else {
								contents.push({
									type: 'link',
									url: linkUrl,
									title: this.getLinkTitle(jsonData, linkUrl, index),
									index: index + 1
								});
							}
						});

						return contents;
					} else {
						// JSON中没有链接，美化显示JSON内容
						contents.push({
							type: 'text',
							text: this.formatJsonOutput(jsonData)
						});
						return contents;
					}
				}
			} catch (e) {
				// 如果不是JSON格式，继续使用正则表达式解析
			}

			// 使用正则表达式解析普通文本中的链接
			const linkRegex = /https?:\/\/[^\s\)\]\}\,\.\!\?\;\"\']+/gi;
			let lastIndex = 0;
			let match;

			// 收集所有链接
			while ((match = linkRegex.exec(output)) !== null) {
				const cleanUrl = this.cleanUrl(match[0]);
				allLinks.push({
					url: cleanUrl,
					start: match.index,
					end: match.index + match[0].length
				});
			}

			// 按位置排序
			allLinks.sort((a, b) => a.start - b.start);

			// 构建内容数组
			allLinks.forEach((link, index) => {
				// 添加前面的文本内容
				if (link.start > lastIndex) {
					const textContent = output.substring(lastIndex, link.start).trim();
					if (textContent) {
						contents.push({
							type: 'text',
							text: textContent
						});
					}
				}

				// 判断链接类型并添加
				const isImage = this.isImageUrl(link.url);
				const isVideo = this.isVideoUrl(link.url);

				if (isImage) {
					contents.push({
						type: 'image',
						url: link.url, // 直接使用原始链接
						originalUrl: link.url,
						index: index + 1
					});
				} else if (isVideo) {
					contents.push({
						type: 'video',
						url: link.url, // 直接使用原始链接
						originalUrl: link.url,
						index: index + 1
					});
				} else {
					contents.push({
						type: 'link',
						url: link.url,
						title: `链接 ${index + 1}`,
						index: index + 1
					});
				}

				lastIndex = link.end;
			});

			// 添加最后的文本内容
			if (lastIndex < output.length) {
				const textContent = output.substring(lastIndex).trim();
				if (textContent) {
					contents.push({
						type: 'text',
						text: textContent
					});
				}
			}

			// 如果没有找到任何链接，整个内容作为文本
			if (contents.length === 0) {
				contents.push({
					type: 'text',
					text: output.trim()
				});
			}

			// 过滤掉空的文本内容
			return contents.filter(content => {
				if (content.type === 'text') {
					return content.text && content.text.trim().length > 0;
				}
				return true;
			});
		},

		// 从对象中递归提取所有链接
		extractLinksFromObject(obj, links) {
			if (typeof obj === 'string') {
				// 检查字符串是否是链接
				if (obj.startsWith('http://') || obj.startsWith('https://')) {
					links.push(obj);
				}
			} else if (Array.isArray(obj)) {
				// 如果是数组，递归处理每个元素
				obj.forEach(item => this.extractLinksFromObject(item, links));
			} else if (typeof obj === 'object' && obj !== null) {
				// 如果是对象，递归处理每个值
				Object.values(obj).forEach(value => this.extractLinksFromObject(value, links));
			}
		},

		// 获取图片标题（根据JSON中的键名）
		getImageTitle(jsonData, linkUrl, index) {
			// 查找对应的键名
			for (const [key, value] of Object.entries(jsonData)) {
				if (value === linkUrl) {
					return this.formatKeyName(key);
				}
			}
			return `图片 ${index + 1}`;
		},

		// 获取视频标题（根据JSON中的键名）
		getVideoTitle(jsonData, linkUrl, index) {
			// 查找对应的键名
			for (const [key, value] of Object.entries(jsonData)) {
				if (value === linkUrl) {
					return this.formatKeyName(key);
				}
			}
			return `视频 ${index + 1}`;
		},

		// 获取链接标题（根据JSON中的键名）
		getLinkTitle(jsonData, linkUrl, index) {
			// 查找对应的键名
			for (const [key, value] of Object.entries(jsonData)) {
				if (value === linkUrl) {
					return this.formatKeyName(key);
				}
			}
			return `链接 ${index + 1}`;
		},

		// 格式化键名为友好的显示名称
		formatKeyName(key) {
			const keyMap = {
				'fengmian': '封面图',
				'neiye': '内页图',
				'neiye1': '内页图1',
				'neiye2': '内页图2',
				'neiye3': '内页图3',
				'logo': 'Logo图',
				'banner': '横幅图',
				'avatar': '头像',
				'background': '背景图',
				'thumbnail': '缩略图',
				'cover': '封面',
				'poster': '海报',
				'icon': '图标'
			};

			return keyMap[key] || key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
		},

		// 格式化日期显示
		formatDate(dateString) {
			if (!dateString) return '';
			try {
				const date = new Date(dateString);
				const now = new Date();
				const diff = now - date;
				const minutes = Math.floor(diff / 60000);
				const hours = Math.floor(diff / 3600000);
				const days = Math.floor(diff / 86400000);

				if (minutes < 1) return '刚刚';
				if (minutes < 60) return `${minutes}分钟前`;
				if (hours < 24) return `${hours}小时前`;
				if (days < 7) return `${days}天前`;

				return date.toLocaleDateString('zh-CN', {
					year: 'numeric',
					month: 'short',
					day: 'numeric',
					hour: '2-digit',
					minute: '2-digit'
				});
			} catch (error) {
				return dateString;
			}
		},

		// 美化JSON输出显示
		formatJsonOutput(jsonData) {
			if (typeof jsonData === 'string') {
				return jsonData;
			}

			// 如果是简单的键值对对象，格式化显示
			if (typeof jsonData === 'object' && jsonData !== null && !Array.isArray(jsonData)) {
				const entries = Object.entries(jsonData);
				if (entries.length <= 10) { // 只对简单对象进行格式化
					return entries.map(([key, value]) => {
						const formattedKey = this.formatKeyName(key);
						if (typeof value === 'string' && value.length > 50) {
							return `${formattedKey}: ${value.substring(0, 50)}...`;
						}
						return `${formattedKey}: ${value}`;
					}).join('\n');
				}
			}

			// 复杂对象使用JSON格式，但进行美化
			return JSON.stringify(jsonData, null, 2);
		},

		// 判断是否为图片URL
		isImageUrl(url) {
			return /\.(?:jpg|jpeg|png|gif|webp|bmp|svg)(?:\?[^\s]*)?$/i.test(url);
		},

		// 判断是否为视频URL
		isVideoUrl(url) {
			return /\.(?:mp4|avi|mov|wmv|flv|webm|mkv|m4v)(?:\?[^\s]*)?$/i.test(url);
		},

		// 清理URL，移除末尾的标点符号但保留完整的URL参数
		cleanUrl(url) {
			if (!url) return '';

			// 只移除末尾明显不属于URL的标点符号
			// 保留URL中的所有参数，包括 & = % 等字符
			return url.trim().replace(/[,\.\!\?\;]+$/, '');
		},

		// 获取完整的图片/视频URL
		getFullImageUrl(url) {
			// 如果已经是完整的HTTP/HTTPS URL，直接返回
			if (url.startsWith('http://') || url.startsWith('https://')) {
				return url;
			}

			// 如果是相对路径，拼接IMAGE_BASE_URL
			if (url.startsWith('/')) {
				return IMAGE_BASE_URL + url;
			}

			// 如果是相对路径但不以/开头，添加/
			return IMAGE_BASE_URL + '/' + url;
		},

		// 格式化链接显示文本
		formatLinkDisplay(url) {
			try {
				const urlObj = new URL(url);
				let display = urlObj.hostname;

				// 如果路径不为空，添加路径的一部分
				if (urlObj.pathname && urlObj.pathname !== '/') {
					const pathParts = urlObj.pathname.split('/').filter(part => part);
					if (pathParts.length > 0) {
						display += '/' + pathParts[pathParts.length - 1];
					}
				}

				// 限制显示长度
				if (display.length > 40) {
					display = display.substring(0, 37) + '...';
				}

				return display;
			} catch (error) {
				// 如果URL解析失败，返回截断的原始URL
				return url.length > 40 ? url.substring(0, 37) + '...' : url;
			}
		},

		// 预览图片
		previewImage(imageUrl) {
			uni.previewImage({
				urls: [imageUrl],
				current: imageUrl
			});
		},

		// 打开链接
		openLink(url) {
			// #ifdef H5
			window.open(url, '_blank');
			// #endif

			// #ifdef APP-PLUS
			plus.runtime.openURL(url);
			// #endif

			// #ifdef MP
			uni.setClipboardData({
				data: url,
				success: () => {
					uni.showToast({
						title: '链接已复制到剪贴板',
						icon: 'success'
					});
				}
			});
			// #endif
		},

		// 图片加载成功处理
		onImageLoad(e) {
			// 图片加载成功，可以在这里添加一些处理逻辑
			console.log('图片加载成功');
		},

		// 图片加载失败处理
		onImageError(e) {
			console.log('图片加载失败:', e);
			uni.showToast({
				title: '图片加载失败',
				icon: 'none'
			});
		},

		// 显示图片操作按钮
		showImageActions(url, index, event) {
			// 如果点击的是同一张图片，则隐藏操作按钮
			if (this.activeImageIndex === index) {
				this.activeImageIndex = -1;
			} else {
				this.activeImageIndex = index;
			}
		},

		// 在弹窗中预览图片
		previewImageInModal(url, index) {
			this.activeImageIndex = -1; // 隐藏操作按钮
			uni.previewImage({
				urls: this.taskLinks,
				current: index,
				fail: (err) => {
					console.error('预览图片失败:', err);
					uni.showToast({
						title: '预览失败',
						icon: 'none'
					});
				}
			});
		},

		// 复制图片链接
		copyImageLink(url) {
			this.activeImageIndex = -1; // 隐藏操作按钮
			this.copyText(url);
		},

		// 保存图片到相册
		async saveImageToAlbum(url, index) {
			this.activeImageIndex = -1; // 隐藏操作按钮
			await this.downloadImage(url, index);
		},

		// 隐藏图片操作按钮
		hideImageActions() {
			this.activeImageIndex = -1;
		},

		// 预览图片并显示提示
		previewImageWithToast(url, index) {
			uni.showToast({
				title: '预览图片',
				icon: 'none',
				duration: 1000
			});
			setTimeout(() => {
				this.previewImage(url, index);
			}, 100);
		},

		// 复制图片链接并显示提示
		copyImageWithToast(url) {
			uni.showToast({
				title: '复制链接',
				icon: 'none',
				duration: 1000
			});
			setTimeout(() => {
				this.copyText(url);
			}, 100);
		},

		// 保存图片并显示提示
		async saveImageWithToast(url, index) {
			uni.showToast({
				title: '保存图片',
				icon: 'none',
				duration: 1000
			});
			setTimeout(async () => {
				await this.downloadImage(url, index);
			}, 100);
		},

		// 预览图片
		previewImage(url, index) {
			uni.previewImage({
				urls: this.taskLinks,
				current: index,
				fail: (err) => {
					console.error('预览图片失败:', err);
					uni.showToast({
						title: '预览失败',
						icon: 'none'
					});
				}
			});
		},

		// 下载图片
		async downloadImage(url, index) {
			try {
				uni.showLoading({
					title: '保存中...'
				});

				// 下载图片到本地
				const downloadResult = await new Promise((resolve, reject) => {
					uni.downloadFile({
						url: url,
						success: (res) => {
							console.log('下载结果:', res);
							if (res.statusCode === 200) {
								resolve(res.tempFilePath);
							} else {
								reject(new Error(`下载失败，状态码: ${res.statusCode}`));
							}
						},
						fail: (err) => {
							console.error('下载失败:', err);
							reject(err);
						}
					});
				});

				// 保存到相册
				await new Promise((resolve, reject) => {
					uni.saveImageToPhotosAlbum({
						filePath: downloadResult,
						success: (res) => {
							console.log('保存成功:', res);
							resolve(res);
						},
						fail: (err) => {
							console.error('保存到相册失败:', err);
							reject(err);
						}
					});
				});

				uni.hideLoading();
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});

			} catch (error) {
				uni.hideLoading();
				console.error('保存图片失败:', error);

				// 更详细的错误处理
				if (error.errMsg) {
					if (error.errMsg.includes('auth') || error.errMsg.includes('permission')) {
						uni.showModal({
							title: '权限不足',
							content: '需要相册权限才能保存图片，请在设置中开启权限',
							showCancel: false
						});
					} else if (error.errMsg.includes('network') || error.errMsg.includes('timeout')) {
						uni.showToast({
							title: '网络错误，请重试',
							icon: 'none'
						});
					} else {
						uni.showToast({
							title: `保存失败: ${error.errMsg}`,
							icon: 'none',
							duration: 3000
						});
					}
				} else {
					uni.showToast({
						title: '保存失败，请重试',
						icon: 'none'
					});
				}
			}
		},

		// 图片加载失败处理
		onImageError() {
			uni.showToast({
				title: '图片加载失败',
				icon: 'none'
			});
		},

		// 打开链接
		openLink(url) {
			if (!url) return;

			// 在小程序中使用 web-view 或复制链接
			// #ifdef MP-WEIXIN
			uni.setClipboardData({
				data: url,
				success: () => {
					uni.showToast({
						title: '链接已复制到剪贴板',
						icon: 'success'
					});
				}
			});
			// #endif

			// 在H5中直接打开
			// #ifdef H5
			window.open(url, '_blank');
			// #endif

			// 在APP中使用系统浏览器打开
			// #ifdef APP-PLUS
			plus.runtime.openURL(url);
			// #endif
		},

		// 复制文本
		copyText(text) {
			if (!text) return;

			uni.setClipboardData({
				data: text,
				success: () => {
					uni.showToast({
						title: '已复制到剪贴板',
						icon: 'success'
					});
				},
				fail: () => {
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					});
				}
			});
		},

		// 视频加载失败处理
		onVideoError() {
			uni.showToast({
				title: '视频加载失败',
				icon: 'none'
			});
		},

		// 保存图片
		saveImage(url) {
			uni.showLoading({
				title: '保存中...'
			});

			uni.downloadFile({
				url: url,
				success: (res) => {
					if (res.statusCode === 200) {
						uni.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success: () => {
								uni.hideLoading();
								uni.showToast({
									title: '保存成功',
									icon: 'success'
								});
							},
							fail: () => {
								uni.hideLoading();
								uni.showToast({
									title: '保存失败',
									icon: 'none'
								});
							}
						});
					} else {
						uni.hideLoading();
						uni.showToast({
							title: '下载失败',
							icon: 'none'
						});
					}
				},
				fail: () => {
					uni.hideLoading();
					uni.showToast({
						title: '下载失败',
						icon: 'none'
					});
				}
			});
		},

		// 保存视频
		saveVideo(url) {
			uni.showLoading({
				title: '保存中...'
			});

			uni.downloadFile({
				url: url,
				success: (res) => {
					if (res.statusCode === 200) {
						uni.saveVideoToPhotosAlbum({
							filePath: res.tempFilePath,
							success: () => {
								uni.hideLoading();
								uni.showToast({
									title: '保存成功',
									icon: 'success'
								});
							},
							fail: () => {
								uni.hideLoading();
								uni.showToast({
									title: '保存失败',
									icon: 'none'
								});
							}
						});
					} else {
						uni.hideLoading();
						uni.showToast({
							title: '下载失败',
							icon: 'none'
						});
					}
				},
				fail: () => {
					uni.hideLoading();
					uni.showToast({
						title: '下载失败',
						icon: 'none'
					});
				}
			});
		},

		// 打开链接
		openLink(url) {
			// #ifdef H5
			window.open(url, '_blank');
			// #endif

			// #ifdef APP-PLUS
			plus.runtime.openURL(url);
			// #endif

			// #ifdef MP
			uni.setClipboardData({
				data: url,
				success: () => {
					uni.showToast({
						title: '链接已复制',
						icon: 'success'
					});
				}
			});
			// #endif
		},

		// 获取日期键（用于分组）
		getDateKey(dateString) {
			if (!dateString) return '';
			try {
				const date = new Date(dateString);
				return date.toISOString().split('T')[0]; // 返回 YYYY-MM-DD 格式
			} catch (error) {
				return '';
			}
		},

		// 获取日期标签（用于显示）
		getDateLabel(dateString) {
			if (!dateString) return '';
			try {
				const date = new Date(dateString);
				const today = new Date();
				const yesterday = new Date(today);
				yesterday.setDate(yesterday.getDate() - 1);

				// 判断是否是今天
				if (this.isSameDay(date, today)) {
					return '今天';
				}

				// 判断是否是昨天
				if (this.isSameDay(date, yesterday)) {
					return '昨天';
				}

				// 判断是否是本周
				const weekStart = new Date(today);
				weekStart.setDate(today.getDate() - today.getDay());
				if (date >= weekStart) {
					const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
					return weekdays[date.getDay()];
				}

				// 其他日期显示月日
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			} catch (error) {
				return dateString;
			}
		},

		// 判断两个日期是否是同一天
		isSameDay(date1, date2) {
			return date1.getFullYear() === date2.getFullYear() &&
				   date1.getMonth() === date2.getMonth() &&
				   date1.getDate() === date2.getDate();
		}
	}
}
</script>

<style scoped>
.container {
	width: 100%;
	min-height: 100vh;
	padding: 0 0 90px 0;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.header {
	padding: 20px;
	background: #fff;
	text-align: left;
}

.title {
	font-size: 24px;
	font-weight: bold;
	color: #333;
}

.content {
	flex: 1;
	padding: 0 20px 20px 20px;
	border: none;
}



/* 任务类型筛选标签 */
.type-filter {
	display: flex;
	justify-content: center;
	gap: 20px;
	margin-bottom: 30px;
}

.filter-item {
	padding: 8px 20px;
	border-radius: 20px;
	background: #f5f5f5;
	transition: all 0.3s ease;
}

.filter-item.active {
	background: #333;
	color: #fff;
}

.filter-text {
	font-size: 14px;
	color: #666;
}

.filter-item.active .filter-text {
	color: #fff;
}



/* 聊天历史列表样式 */

.history-list {
	background: #fff;
	border-radius: 10px;
	overflow: hidden;
}

.loading-message, .empty-message {
	text-align: center;
	padding: 40px 20px;
	color: #666;
	font-size: 16px;
}

/* 登录提示样式 */
.login-prompt {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80px 40px;
	text-align: center;
}

.prompt-icon {
	font-size: 48px;
	margin-bottom: 20px;
}

.prompt-title {
	font-size: 20px;
	font-weight: 600;
	color: #333;
	margin-bottom: 12px;
}

.prompt-desc {
	font-size: 14px;
	color: #666;
	margin-bottom: 30px;
	line-height: 1.5;
}

.login-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 25px;
	padding: 12px 30px;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.login-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.login-btn:active {
	transform: translateY(0);
}

.history-item {
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.history-item:hover {
	background-color: #f8f9fa;
}

.history-item:last-child {
	border-bottom: none;
}

.item-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 10px;
}

/* 智能体头像样式 */
.agent-avatar {
	width: 45px;
	height: 45px;
	border-radius: 50%;
	overflow: hidden;
	margin-right: 12px;
	border: 2px solid #e0e0e0;
	background: #f5f5f5;
}

.avatar-image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

/* 保留原有头像样式作为备用 */
.item-avatar {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: linear-gradient(45deg, #7b68ee, #ff69b4);
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 12px;
}

.avatar-text {
	font-size: 18px;
}

.item-info {
	flex: 1;
	display: flex;
	align-items: center;
	gap: 12px;
}

/* 智能体标题样式 */
.agent-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 2px;
}

/* 任务元信息容器 */
.task-meta-info {
	display: flex;
	gap: 8px;
	align-items: center;
	margin-bottom: 3px;
}

/* 任务类型标签样式 */
.task-type-tag {
	font-size: 11px;
	color: #fff;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 3px 8px;
	border-radius: 12px;
	display: inline-block;
	font-weight: 500;
	box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

/* 智能体ID样式 */
.agent-id {
	font-size: 11px;
	color: #666;
	background: #f0f0f0;
	padding: 2px 6px;
	border-radius: 8px;
	display: inline-block;
	font-family: monospace;
	border: 1px solid #e0e0e0;
}

.item-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 3px;
}

.item-time {
	font-size: 12px;
	color: #999;
}

.item-content {
	padding-left: 52px;
}

.content-text {
	font-size: 14px;
	color: #666;
	line-height: 1.4;
}

.message-pair {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.user-message,
.assistant-message {
	display: flex;
	align-items: flex-start;
	gap: 6px;
}

.message-label {
	font-size: 12px;
	font-weight: bold;
	color: #666;
	min-width: 24px;
}

.user-message .message-label {
	color: #667eea;
}

.assistant-message .message-label {
	color: #764ba2;
}

.message-text {
	font-size: 14px;
	color: #333;
	line-height: 1.4;
	flex: 1;
}

.load-more {
	padding: 15px;
	text-align: center;
	background-color: #f8f9fa;
	cursor: pointer;
}

.load-more-text {
	color: #667eea;
	font-size: 14px;
}

.loading-state {
	padding: 15px;
	text-align: center;
}

.loading-text {
	color: #999;
	font-size: 14px;
}

.empty-state {
	padding: 60px 20px;
	text-align: center;
}

.empty-icon {
	font-size: 48px;
	display: block;
	margin-bottom: 15px;
}

.empty-text {
	font-size: 16px;
	color: #999;
	margin-bottom: 8px;
}

.empty-hint {
	font-size: 14px;
	color: #ccc;
	margin-bottom: 30px;
}

.create-button {
	display: inline-block;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 25px;
	padding: 12px 30px;
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
	transform: translateY(0);
	transition: all 0.3s ease;
}

.create-button:active {
	transform: translateY(2px);
	box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
}

.create-button-text {
	color: #fff;
	font-size: 16px;
	font-weight: 500;
	letter-spacing: 0.5px;
}

/* 时间线容器样式 - 重新设计 */
.timeline-container {
	padding: 0 !important;
	margin: 0 !important;
	position: relative;
	width: 100% !important;
}

/* 连续的垂直时间线 - 贴左边 */
.timeline-line-continuous {
	position: absolute !important;
	left: 15px !important;
	top: 50px !important;
	bottom: 20px !important;
	width: 3px !important;
	background: linear-gradient(180deg, #e91e63 0%, #9c27b0 100%) !important;
	border-radius: 2px !important;
	z-index: 1 !important;
}

.timeline-day-group {
	margin-bottom: 40px !important;
	position: relative !important;
	padding-left: 40px !important;
}

/* 日期标签贴着最左边 */
.timeline-date-badge {
	background: linear-gradient(135deg, #e91e63 0%, #9c27b0 100%) !important;
	border-radius: 20px !important;
	padding: 8px 16px !important;
	margin-bottom: 15px !important;
	margin-left: -40px !important;
	box-shadow: 0 4px 12px rgba(233, 30, 99, 0.3) !important;
	display: inline-block !important;
	position: relative !important;
	z-index: 3 !important;
}

.date-text {
	color: #fff;
	font-size: 14px;
	font-weight: 600;
	white-space: nowrap;
}

/* 时间线内容行 */
.timeline-content-row {
	display: block !important;
	position: relative !important;
	margin-left: 0 !important;
}

/* 时间线圆点容器 - 重新定位 */
.timeline-dot-container {
	width: 20px !important;
	height: 20px !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	position: absolute !important;
	left: -33px !important;
	top: 10px !important;
	z-index: 2 !important;
	flex-shrink: 0 !important;
}

/* 时间线圆点 */
.timeline-dot {
	width: 12px;
	height: 12px;
	background: linear-gradient(135deg, #e91e63 0%, #9c27b0 100%);
	border-radius: 50%;
	border: 3px solid #fff;
	box-shadow: 0 2px 8px rgba(233, 30, 99, 0.3);
}

/* 卡片网格 */
.timeline-cards-grid {
	display: grid;
	grid-template-columns: 1fr 1fr; /* 固定两列 */
	gap: 15px;
	margin-left: 0; /* 与日期标签对齐 */
}

/* 任务相关样式 */
.task-status {
	padding: 4px 8px;
	border-radius: 12px;
	font-size: 12px;
	font-weight: 500;
	white-space: nowrap;
}

.task-details {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.task-output {
	width: 100%;
}

/* 智能内容显示样式 */
.content-display {
	display: flex;
	flex-direction: column;
	gap: 12px;
	width: 100%;
}

.content-item {
	display: flex;
	flex-direction: column;
	gap: 8px;
	padding: 12px;
	background: #f8f9fa;
	border-radius: 8px;
	border-left: 3px solid #667eea;
}

/* 图片内容样式 */
.image-content {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.image-container {
	position: relative;
	overflow: hidden;
	border-radius: 8px;
	border: 1px solid #e0e0e0;
}

.output-image {
	width: 100%;
	max-height: 300px;
	min-height: 100px;
	border-radius: 8px;
	background: #f0f0f0;
	cursor: pointer;
	transition: transform 0.2s ease;
	object-fit: cover;
	display: block;
}

.image-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.3);
	display: flex;
	justify-content: center;
	align-items: center;
	opacity: 0;
	transition: opacity 0.3s ease;
	cursor: pointer;
}

.image-container:hover .image-overlay {
	opacity: 1;
}

.preview-icon {
	font-size: 24px;
	color: #fff;
}

/* 视频内容样式 */
.video-content {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.output-video {
	width: 100%;
	max-height: 300px;
	min-height: 150px;
	border-radius: 8px;
	background: #000;
	border: 1px solid #e0e0e0;
}

/* 链接内容样式 */
.link-content {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.link-preview {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 12px;
	background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	border: 1px solid #e0e0e0;
}

.link-preview:hover {
	background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.link-icon {
	font-size: 16px;
	color: #667eea;
}

.link-text {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	flex: 1;
	word-break: break-all;
}

/* 文本内容样式 */
.text-content {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.content-text {
	font-size: 14px;
	color: #333;
	line-height: 1.6;
	word-break: break-word;
	white-space: pre-wrap;
}

/* 内容操作按钮样式 */
.content-actions {
	display: flex;
	justify-content: flex-end;
	gap: 8px;
	margin-top: 8px;
}

.copy-btn {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 6px 12px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 16px;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
}

.copy-btn:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.copy-btn:active {
	transform: translateY(0);
	box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
}

.copy-btn .btn-icon {
	font-size: 12px;
}

.copy-btn .btn-text {
	font-size: 11px;
	color: #fff;
	font-weight: 500;
}

/* 移动端优化 */
@media (max-width: 750rpx) {
	.content-item {
		padding: 8px;
		gap: 6px;
	}

	/* 时间线移动端优化 */
	.timeline-container {
		padding: 0 15px 0 25px !important;
	}

	.timeline-line-continuous {
		left: 3px !important; /* 更靠左边 */
		width: 2px;
		top: 35px;
	}

	.timeline-dot-container {
		left: -22px !important; /* 圆点在时间线上 */
	}

	.timeline-date-badge {
		margin-left: -25px !important; /* 贴着最左边 */
	}

	.timeline-date-badge {
		padding: 6px 8px;
		margin-bottom: 10px;
	}

	.date-text {
		font-size: 11px;
	}

	.timeline-cards-grid {
		grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
		gap: 12px;
	}

	.timeline-day-group {
		margin-bottom: 30px;
	}

	.output-image {
		max-height: 250px;
	}

	.output-video {
		max-height: 200px;
	}

	.content-actions {
		justify-content: center;
	}

	.copy-btn {
		padding: 5px 10px;
		font-size: 10px;
	}

	.link-preview {
		padding: 10px;
	}

	.content-text {
		font-size: 13px;
	}
}

.task-meta {
	display: flex;
	gap: 12px;
	margin-top: 8px;
}

.meta-item {
	font-size: 12px;
	color: #999;
	padding: 2px 6px;
	background-color: #f0f0f0;
	border-radius: 8px;
}

/* 操作按钮样式 */
.action-buttons {
	display: flex;
	gap: 10px;
	margin-top: 12px;
	padding-top: 10px;
	border-top: 1px solid #f0f0f0;
}

.action-btn {
	display: flex;
	align-items: center;
	gap: 5px;
	padding: 8px 12px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20px;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.action-btn:active {
	transform: translateY(1px);
	box-shadow: 0 1px 4px rgba(102, 126, 234, 0.3);
}

.copy-input-btn {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.copy-output-btn {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.copy-info-btn {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.btn-icon {
	font-size: 14px;
}

.btn-text {
	font-size: 12px;
	color: #fff;
	font-weight: 500;
}

/* 智能体应用信息样式 */
.agent-app-info {
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	border-radius: 8px;
	padding: 12px;
	margin-bottom: 12px;
	border-left: 4px solid #667eea;
}

.app-info-item {
	display: flex;
	align-items: center;
	margin-bottom: 6px;
}

.app-info-item:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 12px;
	color: #666;
	font-weight: 500;
	min-width: 80px;
}

.info-value {
	font-size: 12px;
	color: #333;
	flex: 1;
}

.agent-id-full {
	font-family: monospace;
	background: #fff;
	padding: 2px 6px;
	border-radius: 4px;
	border: 1px solid #e0e0e0;
}

/* 卡片式布局样式 */
.history-grid {
	padding: 10px;
	border: none;
}

.grid-container {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15px;
	padding: 0 5px;
	border: none;
}

/* 卡片包装器 */
.history-card-wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
	border: none;
}

/* 主卡片样式 */
.history-card {
	position: relative;
	width: 100%;
	height: 140px; /* 调整高度适应两列布局 */
	border-radius: 16px;
	overflow: hidden;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
	cursor: pointer;
}

.history-card:hover {
	transform: translateY(-3px);
	box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* 背景图标铺满整个卡片 */
.card-background-icon {
	width: 100%;
	height: 100%;
	object-fit: cover;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
}

/* 左上角标题覆盖层 */
.card-title-overlay {
	position: absolute;
	bottom: 8px;
	left: 8px;
	right: 8px;
	z-index: 2;
	display: flex;
	justify-content: center;
	align-items: center;
}

.title-text {
	color: white;
	font-size: 14px;
	font-weight: 600;
	text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
	line-height: 1.2;
	text-align: center;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 100%;
}

/* 右上角状态标签 */
.status-badge {
	position: absolute;
	top: 8px;
	right: 8px;
	z-index: 2;
	padding: 4px 8px;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-text {
	font-size: 11px;
	font-weight: 500;
	line-height: 1;
	text-align: center;
}

/* 状态样式 */
.status-success {
	background: #4CAF50;
	box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.status-success .status-text {
	color: white;
}

.status-error {
	background: #F44336;
	box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.status-error .status-text {
	color: white;
}

.status-running {
	background: #FF9800;
	box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
}

.status-running .status-text {
	color: white;
}

.status-default {
	background: #9E9E9E;
	box-shadow: 0 2px 4px rgba(158, 158, 158, 0.3);
}

.status-default .status-text {
	color: white;
}

/* 背景图标铺满整个卡片 */
.card-background-icon {
	width: 100%;
	height: 100%;
	object-fit: cover;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
}



/* 左上角消耗点数标签 */
.consumption-badge {
	position: absolute;
	top: 8px;
	left: 8px;
	border-radius: 10px;
	padding: 2px 5px;
	z-index: 3;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
	background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	height: 16px;
}

.consumption-badge .consumption-text {
	color: white;
	font-size: 9px;
	font-weight: 600;
	line-height: 1;
}

/* 右上角状态 */
.status-badge {
	position: absolute;
	top: 8px;
	right: 8px;
	border-radius: 10px;
	padding: 3px 6px;
	z-index: 3;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.status-success {
	background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
}

.status-error {
	background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
}

.status-default {
	background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.status-badge .status-text {
	color: white;
	font-size: 9px;
	font-weight: 600;
}

/* 底部悬浮按钮 */
.card-action-floating {
	position: absolute;
	bottom: 10px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 3;
}

.view-btn-floating {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 20px;
	padding: 8px 16px;
	font-size: 11px;
	font-weight: 500;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
	backdrop-filter: blur(10px);
}

.view-btn-floating:active {
	transform: translateX(-50%) translateY(1px);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 全新设计的弹窗样式 */
.new-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
	padding: 20px;
	backdrop-filter: blur(4px);
}

.new-modal-container {
	background: white;
	border-radius: 20px;
	width: 95%;
	max-width: 600px;
	max-height: 85vh;
	overflow: hidden;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
	animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: translateY(-20px) scale(0.95);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

.new-modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 24px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	position: relative;
}

.header-content {
	flex: 1;
}

.modal-title {
	font-size: 18px;
	font-weight: 700;
	margin-bottom: 4px;
}

.modal-subtitle {
	font-size: 12px;
	opacity: 0.8;
}

.close-button {
	background: rgba(255, 255, 255, 0.2);
	border: none;
	border-radius: 50%;
	width: 36px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.close-button:hover {
	background: rgba(255, 255, 255, 0.3);
	transform: scale(1.1);
}

.close-icon {
	color: white;
	font-size: 16px;
	font-weight: bold;
}

.new-modal-body {
	padding: 24px;
	max-height: 70vh;
	overflow-y: auto;
	background: #f8fafc;
}

/* 信息卡片样式 */
.info-card {
	background: white;
	border-radius: 16px;
	margin-bottom: 20px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
	overflow: hidden;
	border: 1px solid #e2e8f0;
}

.info-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 20px;
	background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
	border-bottom: 1px solid #e2e8f0;
}

.info-title {
	font-size: 15px;
	font-weight: 700;
	color: #2d3748;
}

.result-count {
	background: #667eea;
	color: white;
	padding: 4px 10px;
	border-radius: 12px;
	font-size: 11px;
	font-weight: 600;
}

.info-content {
	padding: 20px;
}



.input-text {
	color: #2d3748;
	font-size: 14px;
	line-height: 1.6;
	background: #e6fffa;
	padding: 16px;
	border-radius: 12px;
	border-left: 4px solid #38b2ac;
}

/* 结果项样式 */
.result-item {
	background: white;
	border-radius: 12px;
	margin-bottom: 16px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
	border: 1px solid #e2e8f0;
	overflow: hidden;
}

.result-item:last-child {
	margin-bottom: 0;
}

/* 文本结果样式 */
.text-result {
	padding: 20px;
}

.text-content {
	color: #2d3748;
	font-size: 14px;
	line-height: 1.7;
	word-break: break-word;
}

/* 媒体结果样式 */
.media-result {
	overflow: hidden;
}

.media-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 20px;
	background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
	border-bottom: 1px solid #e2e8f0;
}

.media-info {
	display: flex;
	align-items: center;
	gap: 12px;
}

.media-title {
	font-size: 14px;
	font-weight: 600;
	color: #2d3748;
}

.media-badge {
	padding: 4px 8px;
	border-radius: 8px;
	font-size: 10px;
	font-weight: 700;
	color: white;
}

.image-badge {
	background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
}

.video-badge {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

/* 媒体预览样式 */
.media-preview {
	padding: 20px;
	text-align: center;
	position: relative;
}

.media-image {
	width: 100%;
	max-height: 300px;
	border-radius: 12px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.media-video {
	width: 100%;
	max-height: 300px;
	border-radius: 12px;
}

.error-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	background: rgba(0, 0, 0, 0.8);
	color: white;
	padding: 12px 16px;
	border-radius: 8px;
	text-align: center;
}

.error-text {
	font-size: 12px;
	color: white;
}

/* 媒体操作按钮样式 */
.media-actions {
	display: flex;
	gap: 8px;
	padding: 16px 20px;
	background: #f8fafc;
	border-top: 1px solid #e2e8f0;
}

/* 链接结果样式 */
.link-result {
	overflow: hidden;
}

.link-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 20px;
	background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
	border-bottom: 1px solid #e2e8f0;
}

.link-info {
	display: flex;
	align-items: center;
	gap: 12px;
}

.link-title {
	font-size: 14px;
	font-weight: 600;
	color: #2d3748;
}

.link-badge {
	background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
	color: white;
	padding: 4px 8px;
	border-radius: 8px;
	font-size: 10px;
	font-weight: 700;
}

.link-content {
	padding: 20px;
}

.link-url-container {
	background: #f7fafc;
	border: 2px dashed #cbd5e0;
	border-radius: 12px;
	padding: 16px;
	margin-bottom: 16px;
}

.link-url {
	color: #4299e1;
	font-size: 13px;
	line-height: 1.5;
	word-break: break-all;
	font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.link-actions {
	display: flex;
	gap: 8px;
	padding: 16px 20px;
	background: #f8fafc;
	border-top: 1px solid #e2e8f0;
}

/* 操作按钮样式 */
.action-btn {
	display: flex;
	align-items: center;
	gap: 6px;
	padding: 10px 16px;
	border: none;
	border-radius: 10px;
	font-size: 12px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	flex: 1;
	justify-content: center;
}

.primary-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.primary-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.secondary-btn {
	background: #f7fafc;
	color: #4a5568;
	border: 1px solid #e2e8f0;
}

.secondary-btn:hover {
	background: #edf2f7;
	transform: translateY(-1px);
}

.btn-icon {
	font-size: 14px;
}

.btn-text {
	font-size: 12px;
	font-weight: 600;
}







/* 链接按钮样式 */
.link-copy-btn {
	background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
	color: white;
	border: none;
	border-radius: 8px;
	padding: 10px 15px;
	font-size: 12px;
	cursor: pointer;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
}

.link-copy-btn:active {
	transform: translateY(1px);
	box-shadow: 0 1px 2px rgba(66, 153, 225, 0.3);
}

.link-copy-btn .link-text {
	color: white;
	text-decoration: none;
	margin-bottom: 0;
	flex: 1;
	text-align: left;
	font-weight: 500;
}

.link-copy-btn .copy-icon {
	font-size: 14px;
	margin-left: 8px;
}

/* 移动端适配 */
@media (max-width: 750rpx) {
	.grid-container {
		grid-template-columns: repeat(2, 1fr);
		gap: 10px;
	}

	.history-card {
		border-radius: 12px;
		height: 120px; /* 小屏幕减少卡片高度 */
	}

	/* 时间线布局移动端适配 */
	.timeline-container {
		padding: 0 10px 0 22px !important;
	}

	.timeline-line-continuous {
		left: 2px !important; /* 更靠左边 */
		width: 2px;
		top: 30px;
	}

	.timeline-dot-container {
		left: -20px !important; /* 圆点在时间线上 */
	}

	.timeline-date-badge {
		margin-left: -22px !important; /* 贴着最左边 */
	}

	.timeline-cards-grid {
		grid-template-columns: 1fr; /* 小屏幕单列显示 */
		gap: 12px;
	}

	.timeline-date-badge {
		padding: 6px 12px;
		margin-bottom: 12px;
	}

	.date-text {
		font-size: 12px;
	}

	.card-header {
		height: 100px;
	}

	.agent-name {
		font-size: 12px;
	}

	.create-time {
		font-size: 10px;
	}

	.input-preview {
		font-size: 12px;
	}

	.modal-content {
		width: 95%;
		margin: 10px;
	}

	.modal-body {
		padding: 15px;
	}
}

/* 任务详情弹窗样式 */
.task-detail-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(4px);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
	animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
	from { opacity: 0; }
	to { opacity: 1; }
}

.modal-content {
	background: white;
	border-radius: 20px;
	width: 90%;
	max-width: 600px;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
	animation: slideUp 0.3s ease;
}

@keyframes slideUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 24px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.header-info {
	flex: 1;
}

.task-name {
	font-size: 18px;
	font-weight: 600;
	display: block;
	margin-bottom: 4px;
}

.task-time {
	font-size: 12px;
	opacity: 0.8;
}

.close-btn {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.close-btn:hover {
	background: rgba(255, 255, 255, 0.3);
	transform: scale(1.1);
}

.close-icon {
	font-size: 20px;
	font-weight: bold;
}

.modal-body {
	padding: 24px;
	max-height: 60vh;
	overflow-y: auto;
}

/* 信息卡片样式 */
.info-card {
	background: #f8fafc;
	border-radius: 12px;
	margin-bottom: 16px;
	overflow: hidden;
	border: 1px solid #e2e8f0;
}

.card-header {
	background: white;
	padding: 12px 16px;
	border-bottom: 1px solid #e2e8f0;
}

.card-title {
	font-size: 14px;
	font-weight: 600;
	color: #2d3748;
}

.card-content {
	padding: 16px;
}

.points-text {
	font-size: 16px;
	font-weight: 600;
	color: #667eea;
}

.input-text, .output-text {
	font-size: 14px;
	line-height: 1.6;
	color: #4a5568;
	word-break: break-word;
}

/* 链接行样式 */
.link-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 0;
	border-bottom: 1px solid #f1f5f9;
}

.link-row:last-child {
	border-bottom: none;
}

.link-info {
	flex: 1;
	min-width: 0; /* 允许文本截断 */
}

.link-label {
	display: block;
	font-size: 12px;
	font-weight: 600;
	color: #48bb78;
	margin-bottom: 4px;
}

.link-url-short {
	display: block;
	font-size: 13px;
	color: #64748b;
	font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
	word-break: break-all;
	line-height: 1.4;
}

.copy-link-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 6px;
	padding: 8px 12px;
	font-size: 11px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	margin-left: 12px;
	flex-shrink: 0;
	box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.copy-link-btn:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.copy-link-btn:active {
	transform: translateY(0);
	box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.copy-btn-text {
	color: white;
	font-size: 11px;
	font-weight: 600;
}

/* 图片网格样式 */
.image-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
	gap: 16px;
	padding: 8px 0;
}

.image-item {
	display: flex;
	flex-direction: column;
	border-radius: 12px;
	overflow: hidden;
	background: white;
	border: 1px solid #e2e8f0;
	transition: all 0.3s ease;
}

.image-item:hover {
	transform: translateY(-2px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.image-container {
	position: relative;
	width: 100%;
	height: 200px;
	overflow: hidden;
	background: #f7fafc;
}

.generated-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: all 0.3s ease;
}

.image-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: all 0.3s ease;
}

.image-container:hover .image-overlay {
	opacity: 1;
}

.image-actions {
	display: flex;
	gap: 12px;
}

.action-btn {
	width: 44px;
	height: 44px;
	border-radius: 50%;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	cursor: pointer;
	backdrop-filter: blur(10px);
}

.action-btn:active {
	transform: scale(0.9);
}

.preview-btn {
	background: rgba(74, 144, 226, 0.9);
}

.copy-btn {
	background: rgba(52, 199, 89, 0.9);
}

.download-btn {
	background: rgba(255, 149, 0, 0.9);
}

.btn-icon {
	font-size: 18px;
	color: white;
}

.image-info {
	padding: 12px 16px;
	background: white;
}

.image-label {
	font-size: 13px;
	font-weight: 600;
	color: #2d3748;
	display: block;
	margin-bottom: 4px;
}

.image-url {
	font-size: 11px;
	color: #64748b;
	word-break: break-all;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 生成结果中的图片样式 */
.result-images {
	margin-top: 8px;
}

.result-image-grid {
	display: grid;
	grid-template-columns: 1fr 1fr; /* 固定两列 */
	gap: 12px;
}

.result-image-item {
	position: relative;
	border-radius: 8px;
	overflow: hidden;
	background: #f7fafc;
	border: 1px solid #e2e8f0;
}

.result-image {
	width: 100%;
	height: 180px;
	object-fit: cover;
	cursor: pointer;
	transition: all 0.3s ease;
}

.result-image:hover {
	transform: scale(1.02);
}

/* 图片覆盖层和操作按钮 */
.image-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: all 0.3s ease;
}

.result-image-item:hover .image-overlay {
	opacity: 1;
}

.image-actions {
	display: flex;
	gap: 12px;
}

.action-btn {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	border: none;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}

.action-btn:hover {
	transform: scale(1.1);
	background: rgba(255, 255, 255, 1);
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.action-btn:active {
	transform: scale(0.95);
}

.preview-btn:hover {
	background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
	color: white;
}

.copy-btn:hover {
	background: linear-gradient(135deg, #34C759 0%, #28A745 100%);
	color: white;
}

.download-btn:hover {
	background: linear-gradient(135deg, #FF9500 0%, #E8890C 100%);
	color: white;
}

.btn-icon {
	font-size: 18px;
}

@keyframes fadeIn {
	from { opacity: 0; }
	to { opacity: 1; }
}
</style>
