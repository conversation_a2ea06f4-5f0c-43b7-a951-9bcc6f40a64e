import axios from 'axios';
import configApi, { ConfigType } from './config';

// 类型定义
export interface CozeTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface CozeBotItem {
  bot_id: string;
  name: string;
  description?: string;
  avatar_url?: string;
}

export interface CozeBotsResponse {
  success: boolean;
  items: CozeBotItem[];
}

export interface CozeMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface CozeChatRequest {
  bot_id: string;
  messages: CozeMessage[];
  stream?: boolean;
}

export interface CozeChatResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// 工作流相关类型定义
export interface CozeWorkflowRunRequest {
  workflow_id: string;
  parameters?: Record<string, any>;
  user_id?: string;
  stream?: boolean;
  consumption?: number; // 添加消耗点数字段
}

export interface CozeWorkflowRunResponse {
  code: number;
  data: {
    execute_id?: string;
    run_id?: string;
    status?: string;
    result?: any;
    output?: any;
  };
  message?: string;
}

export interface CozeWorkflowStatusResponse {
  code: number;
  data: {
    status: string;
    result?: any;
    output?: any;
    execute_id?: string;
    histories?: any[];
  };
  message?: string;
}

export interface CozeWorkflow {
  workflow_id: string;
  name: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

// 认证方式枚举
export enum CozeAuthMode {
  BASIC = 'basic',  // 基本认证（client_id + client_secret）
  JWT = 'jwt'       // JWT认证（私钥签名）
}

// Coze配置接口
export interface CozeConfig {
  clientId?: string;
  clientSecret?: string;
  authMode?: CozeAuthMode;
  accessToken?: string;
  tokenExpiry?: number;
  [key: string]: any; // 允许其他可能的属性
}

// 从本地存储获取Coze配置
function getCozeConfig(): CozeConfig | null {
  try {
    const config = localStorage.getItem('coze_config');
    if (config) {
      return JSON.parse(config);
    }
  } catch (error) {
    console.error('获取Coze配置失败:', error);
  }
  return null;
}

// 保存Coze配置到本地存储
function saveCozeConfig(config: CozeConfig): void {
  try {
    localStorage.setItem('coze_config', JSON.stringify(config));
  } catch (error) {
    console.error('保存Coze配置失败:', error);
  }
}

// 获取API基础URL
function getApiBaseUrl(): string {
  // 检查运行时配置
  if (typeof window !== 'undefined' && (window as any).ENV_CONFIG && (window as any).ENV_CONFIG.API_BASE_URL) {
    return (window as any).ENV_CONFIG.API_BASE_URL;
  }
  
  // 检查环境变量
  if (import.meta && import.meta.env && import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL as string;
  }
  
  // 默认使用相对路径（由开发服务器代理）
  return '';
}

// 创建axios实例
const API_BASE_URL = getApiBaseUrl();
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 扣子API客户端类
class CozeApiClient {
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;
  private clientId: string | null = null;
  private clientSecret: string | null = null;
  private authMode: CozeAuthMode = CozeAuthMode.BASIC;
  private initialized: boolean = false;
  
  constructor() {
    // 尝试从本地存储恢复令牌和凭据
    const config = getCozeConfig();
    if (config) {
      this.accessToken = config.accessToken || null;
      this.tokenExpiry = config.tokenExpiry || 0;
      this.clientId = config.clientId || null;
      this.clientSecret = config.clientSecret || null;
      this.authMode = config.authMode || CozeAuthMode.BASIC;
    }
    
    // 初始化时尝试从系统配置加载
    this.loadFromSystemConfig();
  }
  
  // 从系统配置加载凭据
  async loadFromSystemConfig() {
    try {
      const response = await configApi.getConfig(ConfigType.KOUZI);
      if (response && response.data && response.data.length > 0) {
        const configs = response.data;
        let appId = '';
        let appSecret = '';
        let authMode = CozeAuthMode.BASIC;
        let enabled = false;
        
        // 遍历配置项
        configs.forEach(config => {
          if (config.key === 'appId') {
            appId = config.value;
          } else if (config.key === 'appSecret') {
            appSecret = config.value;
          } else if (config.key === 'authMode') {
            authMode = config.value as CozeAuthMode;
          } else if (config.key === 'enabled') {
            enabled = config.value === 'true';
          }
        });
        
        // 如果配置有效，设置凭据
        if (appId && (authMode === CozeAuthMode.BASIC ? appSecret : true) && enabled) {
          this.setCredentials(appId, appSecret, authMode);
          this.initialized = true;
        }
      }
    } catch (error) {
      console.error('从系统配置加载Coze凭据失败:', error);
    }
  }
  
  // 设置API凭据
  setCredentials(clientId: string, clientSecret: string, authMode: CozeAuthMode = CozeAuthMode.BASIC) {
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.authMode = authMode;
    
    // 保存凭据到本地存储
    saveCozeConfig({
      clientId,
      clientSecret,
      authMode,
      accessToken: this.accessToken,
      tokenExpiry: this.tokenExpiry
    });
    
    this.initialized = true;
  }
  
  // 获取OAuth令牌
  async getToken(clientId?: string, clientSecret?: string, authMode?: CozeAuthMode): Promise<string> {
    // 如果提供了新的凭据，更新存储的凭据
    if (clientId && clientSecret) {
      this.setCredentials(clientId, clientSecret, authMode || this.authMode);
    }
    
    // 检查是否已有有效令牌
    if (this.accessToken && this.tokenExpiry > Date.now()) {
      return this.accessToken;
    }
    
    // 如果未初始化，尝试从系统配置加载
    if (!this.initialized) {
      await this.loadFromSystemConfig();
    }
    
    // 检查是否有存储的凭据
    if (!this.clientId) {
      throw new Error('未设置Coze API凭据，请先调用setCredentials方法或确保系统配置中有有效的Coze配置');
    }
    
    try {
      const requestConfig: any = {};
      let data: any = {
        client_id: this.clientId,
        grant_type: 'client_credentials'
      };
      
      // 根据认证模式设置请求
      if (this.authMode === CozeAuthMode.JWT) {
        // JWT认证模式，设置请求头标记使用JWT
        requestConfig.headers = {
          'X-Use-JWT': 'true'
        };
        
        // 不需要在请求体中包含client_secret
        data = {
          client_id: this.clientId,
          grant_type: 'client_credentials'
        };
      } else {
        // 基本认证模式，需要client_secret
        if (!this.clientSecret) {
          throw new Error('使用基本认证模式时必须提供Client Secret');
        }
        
        data = {
          client_id: this.clientId,
          client_secret: this.clientSecret,
          grant_type: 'client_credentials'
        };
      }
      
      // 发送请求获取令牌
      const response = await apiClient.post<CozeTokenResponse>('/api/proxy/oauth/token', data, requestConfig);
      
      const { access_token, expires_in } = response.data;
      this.accessToken = access_token;
      // 设置过期时间（提前5分钟过期，确保有足够时间刷新）
      this.tokenExpiry = Date.now() + (expires_in - 300) * 1000;
      
      // 保存令牌到本地存储
      saveCozeConfig({
        clientId: this.clientId,
        clientSecret: this.clientSecret,
        authMode: this.authMode,
        accessToken: this.accessToken,
        tokenExpiry: this.tokenExpiry
      });
      
      return this.accessToken;
    } catch (error) {
      console.error('获取Coze OAuth令牌失败:', error);
      throw new Error('获取Coze OAuth令牌失败');
    }
  }
  
  // 确保请求头中包含有效的授权令牌
  private async ensureToken(): Promise<string> {
    // 获取有效令牌
    const token = await this.getToken();
    return `Bearer ${token}`;
  }
  
  // 获取智能体列表
  async getBots(): Promise<CozeBotsResponse> {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();
      
      const response = await apiClient.get<CozeBotsResponse>('/api/proxy/bots', {
        headers: {
          'Authorization': authorization
        }
      });
      return response.data;
    } catch (error) {
      console.error('获取Coze智能体列表失败:', error);
      throw new Error('获取Coze智能体列表失败');
    }
  }
  
  // 发送聊天请求
  async chat(botId: string, messages: CozeMessage[]): Promise<CozeChatResponse> {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();
      
      const response = await apiClient.post<CozeChatResponse>('/api/proxy/chat', {
        bot_id: botId,
        messages: messages
      }, {
        headers: {
          'Authorization': authorization
        }
      });
      return response.data;
    } catch (error) {
      console.error('Coze聊天请求失败:', error);
      throw new Error('Coze聊天请求失败');
    }
  }
  
  // 检查是否已初始化
  isInitialized(): boolean {
    return this.initialized;
  }

  // 执行工作流
  async runWorkflow(workflowId: string, parameters: Record<string, any> = {}, options: { userId?: string; stream?: boolean; consumption?: number } = {}): Promise<CozeWorkflowRunResponse> {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();

      // 设置默认选项
      const {
        userId = 'default_user',
        stream = false,
        consumption = 1000000 // 默认消耗点数
      } = options;

      console.log('📊 coze-api.ts 接收到的参数:', {
        workflowId,
        parameters,
        options,
        consumption
      });

      // 构建符合扣子工作流API格式的请求体
      const requestData: CozeWorkflowRunRequest = {
        workflow_id: workflowId,
        parameters: parameters,
        user_id: userId,
        stream: stream,
        consumption: consumption // 添加消耗点数字段
      };

      console.log('🚀 发送工作流执行请求:', requestData);

      const response = await apiClient.post<CozeWorkflowRunResponse>('/api/proxy/workflow/run', requestData, {
        headers: {
          'Authorization': authorization
        },
        timeout: 180000 // 工作流可能需要更长时间，设置为3分钟
      });

      console.log('✅ 工作流执行响应:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ 工作流执行失败:', error);
      throw new Error('工作流执行失败');
    }
  }

  // 获取工作流执行状态
  async getWorkflowRunStatus(runId: string): Promise<CozeWorkflowStatusResponse> {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();

      const response = await apiClient.get<CozeWorkflowStatusResponse>(`/api/proxy/workflow/run/${runId}`, {
        headers: {
          'Authorization': authorization
        }
      });

      return response.data;
    } catch (error) {
      console.error('❌ 获取工作流状态失败:', error);
      throw new Error('获取工作流状态失败');
    }
  }

  // 获取工作流执行结果
  async getWorkflowExecutionResult(executeId: string): Promise<CozeWorkflowStatusResponse> {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();

      const response = await apiClient.get<CozeWorkflowStatusResponse>(`/api/proxy/workflow/execution/${executeId}`, {
        headers: {
          'Authorization': authorization
        }
      });

      return response.data;
    } catch (error) {
      console.error('❌ 获取工作流执行结果失败:', error);
      throw new Error('获取工作流执行结果失败');
    }
  }

  // 获取工作流列表
  async getWorkflows(): Promise<{ success: boolean; items: CozeWorkflow[] }> {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();

      const response = await apiClient.get('/api/proxy/workflows', {
        headers: {
          'Authorization': authorization
        }
      });

      return response.data;
    } catch (error) {
      console.error('❌ 获取工作流列表失败:', error);
      throw new Error('获取工作流列表失败');
    }
  }
}

// 导出Coze API客户端单例
export const cozeApi = new CozeApiClient();

// 导出类型和常量
export const COZE_MESSAGE_ROLES = {
  USER: 'user',
  ASSISTANT: 'assistant'
} as const;

export default cozeApi; 