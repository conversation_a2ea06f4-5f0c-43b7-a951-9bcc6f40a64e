import { Router, Request, Response } from 'express';
import axios from 'axios';
import * as jwt from 'jsonwebtoken';
import { AppDataSource } from '../data-source';
import { Config } from '../entity/Config';
import { getUserId, extractUserIdFromRequest } from '../utils/auth';

// JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'secure_jwt_secret_key_for_ai_agent_admin';

// 创建路由器实例
const router = Router();

// Coze API基础URL - 聊天使用v3版本，工作流使用v1版本
const COZE_API_BASE_URL = 'https://api.coze.cn/v3';
const COZE_WORKFLOW_API_BASE_URL = 'https://api.coze.cn/v1';
// OAuth端点 - 根据官方文档使用正确的端点
const COZE_OAUTH_URL = 'https://api.coze.cn/api/permission/oauth2/token';

// 从数据库获取扣子OAuth应用公钥（用于JWT的kid字段）
async function getPublicKey(): Promise<string | null> {
  try {
    const configRepository = AppDataSource.getRepository(Config);
    const publicKeyConfig = await configRepository.createQueryBuilder('config')
      .where('config.type = :type', { type: 'kouzi' })
      .andWhere('config.key = :key', { key: 'public_key' })
      .andWhere('config.value IS NOT NULL')
      .andWhere('config.value != :empty', { empty: '' })
      .orderBy('config.updated_at', 'DESC')
      .getOne();

    if (publicKeyConfig && publicKeyConfig.value) {
      console.log('✅ 获取到扣子应用公钥');
      return publicKeyConfig.value;
    }

    console.error('❌ 未找到扣子应用公钥配置');
    return null;
  } catch (error) {
    console.error('❌ 获取扣子应用公钥失败:', error);
    return null;
  }
}

// 从数据库获取扣子OAuth应用私钥（用于JWT签名）
async function getPrivateKey(): Promise<string | null> {
  try {
    const configRepository = AppDataSource.getRepository(Config);
    const privateKeyConfig = await configRepository.createQueryBuilder('config')
      .where('config.type = :type', { type: 'kouzi' })
      .andWhere('config.key = :key', { key: 'private_key' })
      .andWhere('config.value IS NOT NULL')
      .andWhere('config.value != :empty', { empty: '' })
      .orderBy('config.updated_at', 'DESC')
      .getOne();

    if (privateKeyConfig && privateKeyConfig.value) {
      console.log('✅ 获取到扣子应用私钥');
      return privateKeyConfig.value;
    }

    console.error('❌ 未找到扣子应用私钥配置');
    return null;
  } catch (error) {
    console.error('❌ 获取扣子应用私钥失败:', error);
    return null;
  }
}

// 从数据库获取扣子应用ID
async function getAppId(): Promise<string | null> {
  try {
    const configRepository = AppDataSource.getRepository(Config);
    const appIdConfig = await configRepository.createQueryBuilder('config')
      .where('config.type = :type', { type: 'kouzi' })
      .andWhere('config.key = :key', { key: 'app_id' })
      .andWhere('config.value IS NOT NULL')
      .andWhere('config.value != :empty', { empty: '' })
      .orderBy('config.updated_at', 'DESC')
      .getOne();

    if (appIdConfig && appIdConfig.value) {
      console.log('✅ 获取到扣子应用ID:', appIdConfig.value);
      return appIdConfig.value;
    }

    console.error('❌ 未找到扣子应用ID配置');
    return null;
  } catch (error) {
    console.error('❌ 获取扣子应用ID失败:', error);
    return null;
  }
}





// 根据官方文档实现OAuth认证流程
async function getOAuthAccessToken(): Promise<string | null> {
  try {
    const appId = await getAppId();
    if (!appId) {
      console.error('未找到扣子应用ID');
      return null;
    }

    // 扣子OAuth使用JWT方式认证
    console.log('🔐 开始扣子JWT OAuth认证...');

    const privateKey = await getPrivateKey();
    if (!privateKey) {
      console.error('❌ 未找到扣子OAuth应用私钥，请在管理后台配置');
      return null;
    }

    // 获取公钥（kid）
    const publicKey = await getPublicKey();
    if (!publicKey) {
      console.error('❌ 未找到扣子OAuth应用公钥，请先在扣子平台创建密钥对');
      return null;
    }

    console.log('✅ 找到JWT认证所需的公钥和私钥');

    // 根据扣子官方文档创建JWT payload
    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iss: appId,           // 应用ID作为issuer
      aud: 'api.coze.cn',   // 固定audience
      iat: now,             // 签发时间
      exp: now + 3600,      // 过期时间（1小时）
      jti: `${appId}-${now}-${Math.random().toString(36).substr(2, 9)}` // 唯一标识符
    };

    console.log('📝 JWT Payload:', {
      iss: payload.iss,
      aud: payload.aud,
      iat: new Date(payload.iat * 1000).toISOString(),
      exp: new Date(payload.exp * 1000).toISOString(),
      jti: payload.jti
    });

    // 使用RS256算法签名JWT，包含kid（公钥）
    const jwtToken = jwt.sign(payload, privateKey, {
      algorithm: 'RS256',
      header: {
        alg: 'RS256',
        typ: 'JWT',
        kid: publicKey  // 关键：包含从扣子平台获取的公钥
      }
    });

    console.log('生成JWT token长度:', jwtToken.length);

    // 根据官方文档调用OAuth端点 - 使用Authorization Header
    const oauthData = {
      grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
      duration_seconds: 86400 // 24小时有效期
    };

    console.log('发送OAuth请求到:', COZE_OAUTH_URL);
    console.log('JWT Token预览:', jwtToken.substring(0, 50) + '...');

    const response = await axios.post(COZE_OAUTH_URL, oauthData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${jwtToken}`,  // 关键：JWT放在Authorization Header中
        'User-Agent': 'CozeProxy/1.0'
      },
      timeout: 15000,
      validateStatus: function (status) {
        return status < 500; // 接受所有小于500的状态码
      }
    });

    console.log('OAuth响应状态:', response.status);
    console.log('OAuth响应数据:', response.data);

    if (response.status === 200 && response.data && response.data.access_token) {
      console.log('✅ 成功获取OAuth访问令牌');
      return response.data.access_token;
    } else {
      console.error('❌ OAuth认证失败');
      console.error('状态码:', response.status);
      console.error('响应数据:', response.data);
      return null;
    }

  } catch (error: any) {
    console.error('❌ OAuth认证异常:', error.message);
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应数据:', error.response.data);
    }
    return null;
  }
}

// 获取个人访问令牌（备用方案）
async function getPersonalAccessToken(): Promise<string | null> {
  try {
    const configRepository = AppDataSource.getRepository(Config);
    const tokenConfig = await configRepository.findOne({
      where: {
        type: 'kouzi',
        key: 'personal_access_token'
      }
    });

    if (tokenConfig && tokenConfig.value) {
      console.log('✅ 获取到个人访问令牌');
      return tokenConfig.value;
    }

    console.log('⚠️ 未找到个人访问令牌配置');
    return null;
  } catch (error) {
    console.error('❌ 获取个人访问令牌失败:', error);
    return null;
  }
}

// 添加中间件设置响应头，确保中文正确显示
router.use((req: Request, res: Response, next) => {
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  next();
});

// 1. OAuth令牌代理 - 使用JWT OAuth认证
router.post('/oauth/token', async (req: Request, res: Response) => {
  console.log('🔐 收到扣子OAuth令牌请求');

  try {
    // 使用JWT OAuth认证
    const accessToken = await getOAuthAccessToken();

    if (accessToken) {
      console.log('✅ JWT OAuth认证成功');
      res.json({
        access_token: accessToken,
        token_type: 'bearer',
        expires_in: 86400 // 24小时
      });
    } else {
      console.log('❌ JWT OAuth认证失败，尝试个人访问令牌');

      // 备用方案：使用个人访问令牌
      const personalToken = await getPersonalAccessToken();
      if (personalToken) {
        console.log('✅ 使用个人访问令牌');
        res.json({
          access_token: personalToken,
          token_type: 'bearer',
          expires_in: 86400
        });
      } else {
        res.status(401).json({
          error: 'authentication_failed',
          error_description: 'JWT OAuth认证失败且未配置个人访问令牌'
        });
      }
    }
  } catch (error: any) {
    console.error('❌ OAuth令牌请求失败:', error.message);
    res.status(500).json({
      error: 'server_error',
      error_description: error.message
    });
  }
});

// 2. 智能体列表代理 - 使用OAuth认证
router.get('/bots', async (req: Request, res: Response) => {
  console.log('📋 获取扣子智能体列表');

  try {
    // 获取访问令牌（优先OAuth，备用个人令牌）
    let accessToken = await getOAuthAccessToken();
    if (!accessToken) {
      accessToken = await getPersonalAccessToken();
    }

    if (!accessToken) {
      return res.status(401).json({
        error: '认证失败',
        message: '无法获取有效的访问令牌'
      });
    }

    // 调用扣子API
    const response = await axios.get('https://api.coze.cn/v1/bots', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('✅ 智能体列表获取成功');
    res.json(response.data);
  } catch (error: any) {
    console.error('❌ 智能体列表获取失败:', error.message);

    if (error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      res.status(500).json({
        error: '请求失败',
        message: error.message
      });
    }
  }
});

// 用于防止重复请求的缓存
const recentChatRequests = new Map();

// 3. 聊天代理 - 扣子API v3格式
router.post('/chat', async (req: Request, res: Response) => {
  console.log('💬 收到扣子聊天请求');

  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({
        error: '缺少授权令牌',
        message: '请提供有效的授权令牌'
      });
    }

    // 从Authorization头中提取用户ID
    const memberId = getUserId(req);

    if (!memberId) {
      console.error('❌ 无法获取用户ID，请检查登录状态');
      return res.status(401).json({
        error: '用户未登录',
        message: '请先登录后再使用聊天功能'
      });
    }

    console.log('🔍 当前用户ID:', memberId);

    const {
      bot_id,
      messages,
      user_id = 'default_user',
      stream = false,
      conversation_id,
      auto_save_history = true,
      additional_messages
    } = req.body;

    // 防重复请求检查 - 在扣除点数之前进行
    if (messages && messages.length > 0) {
      const userMessage = messages[messages.length - 1].content;
      const requestKey = `${memberId}_${bot_id}_${userMessage}`;
      const now = Date.now();

      // 检查是否是重复请求（3分钟内相同请求，因为轮询可能需要很长时间）
      if (recentChatRequests.has(requestKey)) {
        const lastRequestTime = recentChatRequests.get(requestKey);
        if (now - lastRequestTime < 180000) { // 3分钟
          console.log('⚠️ 检测到重复聊天请求，忽略');
          console.log(`上次请求时间: ${new Date(lastRequestTime).toLocaleString()}`);
          console.log(`当前时间: ${new Date(now).toLocaleString()}`);
          console.log(`时间差: ${Math.round((now - lastRequestTime) / 1000)}秒`);
          return res.status(429).json({
            error: '请求过于频繁',
            message: '相同消息请求过于频繁，请稍后再试或发送不同的消息'
          });
        }
      }

      // 记录当前请求
      recentChatRequests.set(requestKey, now);
      console.log('✅ 记录聊天请求:', requestKey);

      // 清理过期的请求记录（保留最近10分钟的记录）
      for (const [key, time] of recentChatRequests.entries()) {
        if (now - time > 600000) { // 10分钟
          recentChatRequests.delete(key);
        }
      }
    }

    // 验证必需参数
    if (!bot_id || !messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        error: '参数错误',
        message: 'bot_id 和 messages 参数是必需的'
      });
    }

    // 构建扣子API请求体 - 根据v3 API格式
    const cozeRequestBody: any = {
      bot_id,
      user_id,
      stream,
      auto_save_history,
      additional_messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        content_type: msg.content_type || 'text'
      }))
    };

    // 如果提供了conversation_id，则添加到请求体中
    if (conversation_id) {
      cozeRequestBody.conversation_id = conversation_id;
    }

    console.log('📤 发送到Coze API的请求体:', JSON.stringify(cozeRequestBody, null, 2));

    // 💰 在发送API请求前先扣除点数（因为调用API就会产生费用）
    let pointsDeducted = false;
    console.log('🔍 准备扣除点数，用户ID:', memberId);

    // 获取智能体配置的消耗点数
    let pointsToDeduct = 50000000; // 默认值
    try {
      if (bot_id) {
        const agentThemeResult = await AppDataSource.query(`
          SELECT consumption FROM agent_theme WHERE agentId = ? LIMIT 1
        `, [bot_id]);

        if (agentThemeResult && agentThemeResult.length > 0 && agentThemeResult[0].consumption) {
          pointsToDeduct = agentThemeResult[0].consumption;
          console.log('✅ 从数据库获取智能体消耗点数:', pointsToDeduct);
        } else {
          console.log('⚠️ 未找到智能体配置，使用默认消耗点数:', pointsToDeduct);
        }
      }
    } catch (error) {
      console.error('❌ 获取智能体消耗点数失败，使用默认值:', error);
    }

    try {
      if (memberId) {
        console.log('💰 开始扣除用户点数...');
        await deductUserPoints(memberId, pointsToDeduct, bot_id, null);
        pointsDeducted = true;
        console.log('💰 点数扣除成功，开始调用Coze API');
      } else {
        console.log('⚠️ 用户ID为空，跳过点数扣除');
      }
    } catch (pointsError: any) {
      console.error('❌ 点数扣除失败:', pointsError.message);
      return res.status(400).json({
        error: '点数不足或扣除失败',
        message: pointsError.message || '点数扣除失败'
      });
    }

    // 转发到扣子API
    const response = await axios.post(`${COZE_API_BASE_URL}/chat`, cozeRequestBody, {
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30秒超时
    });

    console.log('✅ 聊天请求成功');
    console.log('📥 Coze API 响应数据:', JSON.stringify(response.data, null, 2));

    // 检查响应状态，如果是in_progress，需要轮询获取结果
    if (response.data && response.data.code === 0 && response.data.data) {
      const chatData = response.data.data;

      if (chatData.status === 'in_progress') {
        console.log('🔄 聊天状态为进行中，开始智能轮询获取结果...');
        console.log('📊 轮询策略: 30秒 -> 20秒 -> 1分钟 -> 1分钟 -> 1分钟 (最多5次)');

        try {
          // 轮询获取聊天结果
          const finalResult = await pollChatResult(authHeader, chatData.conversation_id, chatData.id);

          console.log('🎉 聊天请求处理成功，轮询次数:', finalResult.data?.polling_attempts || '未知');

          // 保存聊天历史（点数已在前面扣除）
          await saveChatHistoryOnly(memberId, bot_id, messages, finalResult);

          // 返回标准格式的响应
          res.json({
            code: 0,
            msg: 'success',
            data: finalResult.data || finalResult
          });

        } catch (pollError: any) {
          console.error('💥 轮询过程发生错误:', pollError.message);

          // 返回详细的错误信息
          res.status(408).json({
            code: 408,
            msg: '聊天响应超时',
            error: pollError.message,
            details: {
              conversation_id: chatData.conversation_id,
              chat_id: chatData.id,
              polling_strategy: '30秒 -> 20秒 -> 1分钟 -> 1分钟 -> 1分钟',
              max_attempts: 5,
              suggestion: '请稍后重试，或检查网络连接状态'
            }
          });
        }
      } else {
        // 直接返回结果
        console.log('✅ 聊天请求立即完成，无需轮询');

        // 保存聊天历史（点数已在前面扣除）
        await saveChatHistoryOnly(memberId, bot_id, messages, response.data);

        // 返回标准格式的响应
        res.json({
          code: 0,
          msg: 'success',
          data: response.data
        });
      }
    } else {
      // 返回标准格式的响应
      res.json({
        code: 0,
        msg: 'success',
        data: response.data
      });
    }
  } catch (error: any) {
    console.error('❌ 聊天请求失败:', error.message);

    // 详细的错误日志
    if (error.response) {
      console.error('❌ Coze API错误响应:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
      res.status(error.response.status).json({
        error: 'Coze API返回错误',
        message: error.response.data?.message || error.response.statusText,
        details: error.response.data
      });
    } else if (error.request) {
      console.error('❌ 网络请求失败:', error.request);
      res.status(500).json({
        error: '网络请求失败',
        message: '无法连接到Coze API服务器'
      });
    } else {
      console.error('❌ 请求配置错误:', error.message);
      res.status(500).json({
        error: '请求失败',
        message: error.message
      });
    }
  }
});

// 取消聊天请求
router.post('/cancel-chat', async (req, res) => {
  try {
    const { conversation_id, chat_id } = req.body;

    if (!conversation_id || !chat_id) {
      return res.status(400).json({
        code: 400,
        msg: '缺少必要参数：conversation_id 和 chat_id'
      });
    }

    const pollingKey = `${conversation_id}_${chat_id}`;
    const pollingState = activePollingRequests.get(pollingKey);

    if (pollingState) {
      // 标记为已取消
      pollingState.cancelled = true;

      // 清除定时器
      if (pollingState.timeoutId) {
        clearTimeout(pollingState.timeoutId);
      }

      console.log('🛑 用户取消聊天请求:', pollingKey);

      res.json({
        code: 0,
        msg: '聊天请求已取消',
        data: {
          conversation_id,
          chat_id,
          cancelled: true
        }
      });
    } else {
      res.json({
        code: 404,
        msg: '未找到活跃的聊天请求',
        data: {
          conversation_id,
          chat_id,
          cancelled: false
        }
      });
    }
  } catch (error: any) {
    console.error('❌ 取消聊天请求失败:', error);
    res.status(500).json({
      code: 500,
      msg: '取消聊天请求失败',
      error: error.message
    });
  }
});

// 存储活跃的轮询请求，用于取消
const activePollingRequests = new Map<string, { cancelled: boolean; timeoutId?: NodeJS.Timeout }>();

// 轮询获取聊天结果的辅助函数 - 优化版本
async function pollChatResult(authHeader: string, conversationId: string, chatId: string, maxAttempts: number = 5): Promise<any> {
  // 定义轮询等待时间策略（毫秒）
  const waitTimes = [
    30000,  // 第1次：发送请求后等待30秒
    20000,  // 第2次：等待20秒
    60000,  // 第3次：等待1分钟
    60000,  // 第4次：等待1分钟
    60000   // 第5次：等待1分钟
  ];

  // 创建轮询请求标识符
  const pollingKey = `${conversationId}_${chatId}`;

  // 注册轮询请求
  activePollingRequests.set(pollingKey, { cancelled: false });

  console.log('🚀 开始智能轮询策略，最多轮询5次');
  console.log('📋 轮询时间安排: 30秒 -> 20秒 -> 1分钟 -> 1分钟 -> 1分钟');
  console.log('🔑 轮询标识符:', pollingKey);

  try {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        // 检查是否已被取消
        const pollingState = activePollingRequests.get(pollingKey);
        if (pollingState?.cancelled) {
          console.log('🛑 轮询已被用户取消');
          throw new Error('用户取消了请求');
        }

        const waitTime = waitTimes[attempt - 1] || 60000; // 默认1分钟
        const waitTimeSeconds = Math.round(waitTime / 1000);

        console.log(`⏰ 第${attempt}次轮询前等待 ${waitTimeSeconds} 秒...`);

        // 等待指定时间，支持取消
        await new Promise<void>((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            // 再次检查是否被取消
            const currentState = activePollingRequests.get(pollingKey);
            if (currentState?.cancelled) {
              reject(new Error('用户取消了请求'));
            } else {
              resolve();
            }
          }, waitTime);

          // 保存timeout ID以便取消
          if (pollingState) {
            pollingState.timeoutId = timeoutId;
          }
        });

        console.log(`🔄 开始第${attempt}次轮询聊天结果...`);

        // 获取聊天消息列表
        const messagesResponse = await axios.get(`${COZE_API_BASE_URL}/chat/message/list`, {
          params: {
            conversation_id: conversationId,
            chat_id: chatId
          },
          headers: {
            'Authorization': authHeader,
            'Content-Type': 'application/json'
          },
          timeout: 15000 // 增加超时时间到15秒
        });

        console.log(`📥 第${attempt}次轮询响应状态:`, messagesResponse.data?.code);

        if (messagesResponse.data && messagesResponse.data.code === 0 && messagesResponse.data.data) {
          const messages = messagesResponse.data.data;

          // 查找助手的回复消息
          const assistantMessage = messages.find((msg: any) =>
            msg.role === 'assistant' && msg.type === 'answer' && msg.content
          );

          if (assistantMessage) {
            console.log(`✅ 第${attempt}次轮询成功找到助手回复:`, assistantMessage.content.substring(0, 100) + '...');

            // 处理图片链接，转换为Markdown格式
            let processedContent = assistantMessage.content;

            // 检查是否是图片链接
            const imageUrlPattern = /^https?:\/\/.*\.(png|jpg|jpeg|gif|webp)(\?.*)?$/i;
            if (imageUrlPattern.test(processedContent.trim())) {
              // 转换为Markdown图片格式
              processedContent = `![图片](${processedContent.trim()})`;
              console.log('🖼️ 检测到图片链接，转换为Markdown格式');
            }

            // 返回符合前端期望的格式
            return {
              code: 0,
              msg: 'success',
              data: {
                conversation_id: conversationId,
                chat_id: chatId,
                content: processedContent,
                messages: messages,
                polling_attempts: attempt // 记录轮询次数
              }
            };
          } else {
            console.log(`⏳ 第${attempt}次轮询暂未找到助手回复，继续等待...`);
          }
        } else {
          console.log(`⚠️ 第${attempt}次轮询响应异常:`, messagesResponse.data?.msg || '未知错误');
        }

      } catch (error: any) {
        console.error(`❌ 第${attempt}次轮询请求失败:`, error.message);

        if (attempt === maxAttempts) {
          console.error('🚫 已达到最大轮询次数，停止轮询');
          throw new Error(`轮询失败：已尝试${maxAttempts}次，最后错误: ${error.message}`);
        }

        console.log(`🔄 第${attempt}次轮询失败，将继续下一次轮询...`);
      }
    }

    // 所有轮询都失败
    console.error('🚫 所有轮询尝试都失败，返回超时错误');
    throw new Error(`轮询超时：已尝试${maxAttempts}次轮询，均未获得有效响应。请检查网络连接或稍后重试。`);
  } finally {
    // 清理轮询请求记录
    activePollingRequests.delete(pollingKey);
    console.log('🧹 清理轮询请求记录:', pollingKey);
  }
}

// 获取聊天历史记录
router.get('/chat/history', async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({
        error: '缺少授权令牌',
        message: '请提供有效的授权令牌'
      });
    }

    // 从token中解析用户ID
    const memberId = getUserId(req);

    if (!memberId) {
      console.error('❌ 无法获取用户ID，请检查登录状态');
      return res.status(401).json({
        error: '用户未登录',
        message: '请先登录后再查看聊天历史'
      });
    }

    const { page = 1, pageSize = 20, startDate, endDate } = req.query;
    const offset = (Number(page) - 1) * Number(pageSize);

    console.log(`📖 获取用户 ${memberId} 的聊天历史，页码: ${page}, 每页: ${pageSize}, 时间筛选: ${startDate} - ${endDate}`);

    // 确保聊天历史表存在
    await ensureChatHistoryTable();

    // 构建WHERE条件
    let whereCondition = 'WHERE member_id = ?';
    const queryParams: any[] = [memberId];

    if (startDate) {
      whereCondition += ' AND created_at >= ?';
      queryParams.push(String(startDate));
    }

    if (endDate) {
      whereCondition += ' AND created_at < ?';
      queryParams.push(String(endDate));
    }

    // 获取聊天历史记录，同时关联智能体信息
    const historyRecords = await AppDataSource.query(`
      SELECT
        ch.id,
        ch.bot_id,
        ch.conversation_id,
        ch.chat_id,
        ch.user_message,
        ch.assistant_reply,
        ch.message_metadata,
        ch.created_at,
        at.title as agent_name,
        at.icon as agent_avatar
      FROM chat_history ch
      LEFT JOIN agent_theme at ON (ch.bot_id = at.agentId OR ch.bot_id = at.workflowId)
      ${whereCondition.replace('member_id', 'ch.member_id')}
      ORDER BY ch.created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, Number(pageSize), offset]);

    // 获取总记录数
    const totalResult = await AppDataSource.query(`
      SELECT COUNT(*) as total FROM chat_history ch ${whereCondition.replace('member_id', 'ch.member_id')}
    `, queryParams);

    const total = totalResult[0].total;
    const totalPages = Math.ceil(total / Number(pageSize));

    console.log(`📊 找到 ${total} 条聊天记录`);

    res.json({
      code: 200,
      message: '获取聊天历史成功',
      data: {
        records: historyRecords.map((record: any) => ({
          id: record.id,
          botId: record.bot_id,
          conversationId: record.conversation_id,
          chatId: record.chat_id,
          userMessage: record.user_message,
          assistantReply: record.assistant_reply,
          metadata: record.message_metadata,
          createdAt: record.created_at,
          agentName: record.agent_name || 'AI智能体',
          agentAvatar: record.agent_avatar || '/static/images/znt_avatar.png'
        })),
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages
        }
      }
    });
  } catch (error: any) {
    console.error('❌ 获取聊天历史失败:', error);
    res.status(500).json({
      error: '获取聊天历史失败',
      message: error.message
    });
  }
});

// 仅保存聊天历史（点数已提前扣除）
async function saveChatHistoryOnly(memberId: number | null, botId: string, messages: any[], chatResult: any) {
  try {
    if (!memberId) {
      console.log('⚠️ 用户ID为空，跳过历史保存');
      return;
    }

    console.log('💾 开始保存聊天历史...');

    // 保存聊天历史
    await saveChatHistory(memberId, botId, messages, chatResult);

    console.log('✅ 聊天历史保存完成');
  } catch (error) {
    console.error('❌ 保存聊天历史失败:', error);
    // 不抛出错误，避免影响聊天响应
  }
}

// 扣除用户点数
async function deductUserPoints(memberId: number, pointsToDeduct: number, botId: string, chatResult: any = null) {
  try {
    console.log(`💰 开始扣除用户 ${memberId} 的点数: ${pointsToDeduct}`);

    // 获取用户当前点数
    const memberResult = await AppDataSource.query(`
      SELECT points FROM members WHERE id = ?
    `, [memberId]);

    if (!memberResult || memberResult.length === 0) {
      console.error('❌ 用户不存在:', memberId);
      throw new Error('用户不存在，无法扣除点数');
    }

    const currentPoints = memberResult[0].points || 0;
    console.log(`📊 用户当前点数: ${currentPoints}`);

    if (currentPoints < pointsToDeduct) {
      console.error('❌ 用户点数不足，无法扣除');
      throw new Error(`点数不足，当前点数: ${currentPoints}，需要: ${pointsToDeduct}`);
    }

    const newPoints = currentPoints - pointsToDeduct;

    // 开始事务
    await AppDataSource.transaction(async (transactionalEntityManager) => {
      // 更新用户点数
      await transactionalEntityManager.query(`
        UPDATE members SET points = ? WHERE id = ?
      `, [newPoints, memberId]);

      // 记录点数扣除到points_records表
      await transactionalEntityManager.query(`
        INSERT INTO points_records (
          memberId,
          type,
          amount,
          balanceBefore,
          balanceAfter,
          description,
          relatedType,
          relatedId,
          metadata,
          createdAt
        ) VALUES (?, 'app_usage', ?, ?, ?, ?, 'chat', ?, ?, NOW())
      `, [
        memberId,
        -pointsToDeduct, // 负数表示扣除
        currentPoints,   // 扣除前余额
        newPoints,       // 扣除后余额
        `AI聊天消费：${pointsToDeduct}点数`,
        botId,
        JSON.stringify({
          botId: botId,
          pointsDeducted: pointsToDeduct,
          chatId: chatResult?.data?.id || chatResult?.id || 'pending',
          conversationId: chatResult?.data?.conversation_id || chatResult?.conversation_id || 'pending',
          reason: 'ai_chat_usage',
          deductedAt: new Date().toISOString()
        })
      ]);
    });

    console.log(`✅ 点数扣除成功: ${currentPoints} -> ${newPoints}`);
  } catch (error) {
    console.error('❌ 扣除用户点数失败:', error);
    throw error;
  }
}

// 保存聊天历史
async function saveChatHistory(memberId: number, botId: string, messages: any[], chatResult: any) {
  try {
    console.log(`💾 开始保存聊天历史，用户ID: ${memberId}`);

    // 确保聊天历史表存在
    await ensureChatHistoryTable();

    // 提取用户消息和AI回复
    const userMessage = messages[messages.length - 1]; // 最后一条用户消息
    let assistantReply = '';

    // 从聊天结果中提取AI回复
    if (chatResult?.data?.messages) {
      const assistantMessage = chatResult.data.messages.find((msg: any) =>
        msg.role === 'assistant' && msg.type === 'answer' && msg.content
      );
      assistantReply = assistantMessage?.content || '';
    }

    if (!assistantReply && chatResult?.choices?.[0]?.message?.content) {
      assistantReply = chatResult.choices[0].message.content;
    }

    // 保存聊天记录到 chat_history 表
    await AppDataSource.query(`
      INSERT INTO chat_history (
        member_id,
        bot_id,
        conversation_id,
        chat_id,
        user_message,
        assistant_reply,
        message_metadata,
        created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      memberId,
      botId,
      chatResult?.data?.conversation_id || null,
      chatResult?.data?.id || null,
      userMessage?.content || '',
      assistantReply,
      JSON.stringify({
        userMessage: userMessage,
        chatResult: chatResult?.data || chatResult,
        messageCount: messages.length
      })
    ]);

    console.log('✅ 聊天历史保存到 chat_history 表成功');

    // 同时保存到 task 表（用于往档页面显示）
    await saveChatToTaskTable(memberId, botId, messages, chatResult, assistantReply);

    console.log('✅ 聊天历史保存完成');
  } catch (error) {
    console.error('❌ 保存聊天历史失败:', error);
    throw error;
  }
}

// 保存聊天记录到 task 表（用于往档页面显示）
async function saveChatToTaskTable(memberId: number, botId: string, messages: any[], chatResult: any, assistantReply: string) {
  try {
    console.log(`📋 开始保存聊天记录到 task 表，会员ID: ${memberId}`);

    // 获取会员信息
    const memberResult = await AppDataSource.query(`
      SELECT name, phone FROM members WHERE id = ?
    `, [memberId]);

    const userName = memberResult[0]?.name || memberResult[0]?.phone || `用户${memberId}`;
    const userMessage = messages[messages.length - 1]; // 最后一条用户消息

    console.log(`👤 获取到会员信息: ${userName}`);

    // 由于系统使用 members 表而不是 user 表，我们需要临时禁用外键约束检查
    // 或者直接使用 memberId 作为 userId（因为它们在逻辑上是相同的）

    // 获取智能体信息（从 agent_themes 表中获取）
    let agentInfo = {
      botId: botId,
      agentName: 'Coze智能体',
      agentAvatar: '/static/images/znt_avatar.png'
    };

    try {
      // 从 agent_theme 表中查找匹配的智能体信息
      const agentThemeResult = await AppDataSource.query(`
        SELECT title, icon, agentId, workflowId
        FROM agent_theme
        WHERE agentId = ? OR workflowId = ?
        ORDER BY id DESC
        LIMIT 1
      `, [botId, botId]);

      if (agentThemeResult && agentThemeResult.length > 0) {
        const theme = agentThemeResult[0];
        agentInfo = {
          botId: botId,
          agentName: theme.title || 'Coze智能体',
          agentAvatar: theme.icon || '/static/images/znt_avatar.png'
        };
        console.log(`📋 从数据库获取到智能体信息: ${agentInfo.agentName}`);
      } else {
        console.log(`⚠️ 未在数据库中找到智能体 ${botId} 的信息，使用默认值`);
      }
    } catch (error) {
      console.error('❌ 获取智能体信息失败:', error);
      // 继续使用默认值
    }

    // 尝试从数据库获取智能体详细信息
    try {
      const agentResult = await AppDataSource.query(`
        SELECT title as name, icon as avatar, description FROM agent_theme WHERE agentId = ? OR workflowId = ? LIMIT 1
      `, [botId, botId]);

      if (agentResult.length > 0) {
        const agent = agentResult[0];
        agentInfo.agentName = agent.name || 'Coze智能体';
        agentInfo.agentAvatar = agent.avatar || '/static/images/znt_avatar.png';
        console.log(`✅ 获取到智能体信息: ${agentInfo.agentName}`);
      } else {
        console.log(`⚠️ 未找到智能体 ${botId} 的详细信息，使用默认信息`);
      }
    } catch (agentError) {
      console.error('❌ 获取智能体信息失败:', agentError);
    }

    // 构建任务记录，包含智能体信息
    const taskData = {
      memberId: memberId, // 使用 memberId 而不是 userId
      userName: userName,
      taskSource: agentInfo.agentAvatar, // 使用 taskSource 保存智能体图标
      taskType: '智能体',
      taskTypeDetail: agentInfo.agentName, // 使用 taskTypeDetail 保存智能体标题
      consumption: {
        amount: 500,
        tokens: 500,
        inputToken: userMessage?.content?.length || 0,
        outputToken: assistantReply?.length || 0,
        botId: botId,
        agentName: agentInfo.agentName,
        agentAvatar: agentInfo.agentAvatar
      },
      status: '已完成',
      logs: `与${agentInfo.agentName}聊天成功完成，消耗500点数`,
      input: userMessage?.content || '',
      output: assistantReply || '',
      instructions: `与智能体 ${agentInfo.agentName} (${botId}) 进行对话`,
      createTime: new Date(),
      updateTime: new Date(),
      completeTime: new Date()
    };

    // 临时禁用外键约束检查，然后插入到 task 表
    await AppDataSource.query(`SET FOREIGN_KEY_CHECKS = 0`);

    try {
      await AppDataSource.query(`
        INSERT INTO task (
          userId,
          userName,
          taskSource,
          taskType,
          taskTypeDetail,
          consumption,
          status,
          logs,
          input,
          output,
          instructions,
          createTime,
          updateTime,
          completeTime
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())
      `, [
        taskData.memberId, // 使用 memberId 作为 userId
        taskData.userName,
        taskData.taskSource,
        taskData.taskType,
        taskData.taskTypeDetail,
        JSON.stringify(taskData.consumption),
        taskData.status,
        taskData.logs,
        taskData.input,
        taskData.output,
        taskData.instructions
      ]);

      console.log('✅ 聊天记录保存到 task 表成功');
    } finally {
      // 重新启用外键约束检查
      await AppDataSource.query(`SET FOREIGN_KEY_CHECKS = 1`);
    }
  } catch (error) {
    console.error('❌ 保存聊天记录到 task 表失败:', error);
    // 确保重新启用外键约束检查
    try {
      await AppDataSource.query(`SET FOREIGN_KEY_CHECKS = 1`);
    } catch (resetError) {
      console.error('❌ 重新启用外键约束检查失败:', resetError);
    }
    // 不抛出错误，避免影响主要的聊天功能
  }
}

// 确保聊天历史表存在
async function ensureChatHistoryTable() {
  try {
    // 检查表是否存在
    const tableExists = await AppDataSource.query(`
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'chat_history'
    `);

    if (tableExists[0].count === 0) {
      console.log('📋 创建聊天历史表...');

      // 创建聊天历史表
      await AppDataSource.query(`
        CREATE TABLE chat_history (
          id INT PRIMARY KEY AUTO_INCREMENT,
          member_id INT NOT NULL COMMENT '会员ID',
          bot_id VARCHAR(100) NOT NULL COMMENT '机器人ID',
          conversation_id VARCHAR(100) NULL COMMENT '对话ID',
          chat_id VARCHAR(100) NULL COMMENT '聊天ID',
          user_message TEXT NOT NULL COMMENT '用户消息',
          assistant_reply TEXT NOT NULL COMMENT 'AI回复',
          message_metadata JSON NULL COMMENT '消息元数据',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_member_id (member_id),
          INDEX idx_bot_id (bot_id),
          INDEX idx_conversation_id (conversation_id),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天历史记录表'
      `);

      console.log('✅ 聊天历史表创建成功');
    }
  } catch (error) {
    console.error('❌ 确保聊天历史表存在失败:', error);
    throw error;
  }
}

// 工作流执行接口
router.post('/workflow/run', async (req, res) => {
  console.log('📤 收到工作流执行请求');
  console.log('请求体:', JSON.stringify(req.body, null, 2));

  try {
    const { workflow_id, parameters = {}, user_id = 'default_user', stream = false, consumption = 1000 } = req.body;

    if (!workflow_id) {
      return res.status(400).json({
        error: '参数错误',
        message: 'workflow_id 是必填参数'
      });
    }

    console.log('📊 工作流执行参数:', {
      workflow_id,
      parameters,
      user_id,
      stream,
      consumption: consumption
    });

    // 获取访问令牌
    let accessToken = await getOAuthAccessToken();
    if (!accessToken) {
      accessToken = await getPersonalAccessToken();
    }

    if (!accessToken) {
      return res.status(401).json({
        error: '认证失败',
        message: '无法获取有效的访问令牌'
      });
    }

    // 构建扣子工作流API请求体
    const cozeRequestBody = {
      workflow_id,
      parameters,
      user_id,
      stream
    };

    console.log('📤 发送到Coze工作流API的请求体:', JSON.stringify(cozeRequestBody, null, 2));

    // 获取用户ID用于点数扣除
    let memberId = null;
    try {
      const userToken = req.headers['x-user-token'];
      if (userToken && typeof userToken === 'string') {
        const token = userToken.replace('Bearer ', '');
        const decoded = jwt.verify(token, JWT_SECRET) as any;
        memberId = decoded.id;
        console.log('🔍 从用户token解析出用户ID:', memberId);
      }
    } catch (tokenError) {
      console.log('⚠️ 解析用户token失败，将跳过点数扣除:', tokenError);
    }

    // 💰 扣除点数（使用前端传递的消耗点数）
    let pointsDeducted = false;
    try {
      if (memberId) {
        console.log(`💰 开始扣除用户点数，消耗: ${consumption} 点`);
        await deductUserPoints(memberId, consumption, workflow_id, null); // 使用前端传递的消耗点数
        pointsDeducted = true;
        console.log('💰 点数扣除成功，开始调用Coze工作流API');
      } else {
        console.log('⚠️ 用户ID为空，跳过点数扣除');
      }
    } catch (pointsError: any) {
      console.error('❌ 点数扣除失败:', pointsError.message);
      return res.status(400).json({
        error: '点数不足或扣除失败',
        message: pointsError.message || '点数扣除失败'
      });
    }

    // 调用扣子工作流API
    const response = await axios.post(`${COZE_WORKFLOW_API_BASE_URL}/workflow/run`, cozeRequestBody, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      timeout: 180000 // 工作流可能需要更长时间，设置为3分钟
    });

    console.log('✅ 工作流执行成功');
    console.log('📥 Coze工作流API 响应数据:', JSON.stringify(response.data, null, 2));

    // 🖼️ 处理工作流响应中的图片链接
    let processedResponse = { ...response.data };

    // 检查响应数据中是否包含图片链接
    if (processedResponse.data) {
      const checkAndProcessImageUrl = (content: any): any => {
        if (typeof content === 'string') {
          // 检查是否是图片链接
          const imageUrlPattern = /^https?:\/\/.*\.(png|jpg|jpeg|gif|webp)(\?.*)?$/i;
          if (imageUrlPattern.test(content.trim())) {
            console.log('🖼️ 检测到工作流返回图片链接，转换为Markdown格式:', content.trim());
            return `![图片](${content.trim()})`;
          }
        } else if (typeof content === 'object' && content !== null) {
          // 递归处理对象中的所有字符串值
          const processed = { ...content };
          for (const key in processed) {
            processed[key] = checkAndProcessImageUrl(processed[key]);
          }
          return processed;
        }
        return content;
      };

      processedResponse.data = checkAndProcessImageUrl(processedResponse.data);
    }

    // 💾 保存工作流执行结果到task表
    try {
      if (memberId && response.data && response.data.code === 0) {
        console.log('💾 开始保存工作流执行结果到task表...');

        // 获取用户信息
        const userResult = await AppDataSource.query(`
          SELECT name, phone FROM members WHERE id = ?
        `, [memberId]);

        const userName = userResult && userResult.length > 0 ?
          (userResult[0].name || userResult[0].phone || `用户${memberId}`) : 'unknown';

        // 获取工作流信息（图标和标题）
        let workflowIcon = '/static/images/workflow-default.png'; // 默认图标
        let workflowTitle = `工作流 ${workflow_id}`; // 默认标题

        try {
          const workflowResult = await AppDataSource.query(`
            SELECT icon, title FROM agent_theme WHERE workflowId = ? LIMIT 1
          `, [workflow_id]);

          if (workflowResult && workflowResult.length > 0) {
            workflowIcon = workflowResult[0].icon || workflowIcon;
            workflowTitle = workflowResult[0].title || workflowTitle;
          }
        } catch (workflowQueryError: any) {
          console.log('⚠️ 获取工作流信息失败，使用默认值:', workflowQueryError.message);
        }

        // 构建任务数据
        const taskData = {
          userId: memberId,
          userName: userName,
          taskSource: workflowIcon, // 保存工作流图标
          taskType: '工作流', // 保存 "工作流"
          taskTypeDetail: workflowTitle, // 保存工作流标题
          consumption: {
            amount: consumption,
            tokens: consumption,
            inputToken: 0,
            outputToken: 0,
            workflowId: workflow_id,
            workflowTitle: workflowTitle
          },
          status: '已完成', // 保存完成状态
          logs: workflowTitle, // 保存当前执行工作流的名称
          input: (() => {
            // 处理用户输入参数
            if (!parameters || typeof parameters !== 'object') {
              return parameters || '无参数输入';
            }

            const paramKeys = Object.keys(parameters);
            if (paramKeys.length === 0) {
              return '无参数输入';
            }

            // 将参数转换为可读格式
            const paramStrings = paramKeys.map(key => {
              const value = parameters[key];
              return `${key}: ${value}`;
            });

            return paramStrings.join(', ');
          })(), // 保存用户输入的内容
          output: JSON.stringify(response.data.data || response.data),
          instructions: `执行工作流: ${workflowTitle}`
        };

        // 保存到task表
        const insertResult = await AppDataSource.query(`
          INSERT INTO task (
            userId, userName, taskSource, taskType, taskTypeDetail,
            consumption, status, logs, input, output, instructions,
            createTime, updateTime
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          taskData.userId,
          taskData.userName,
          taskData.taskSource,
          taskData.taskType,
          taskData.taskTypeDetail,
          taskData.consumption,
          taskData.status,
          taskData.logs,
          taskData.input,
          taskData.output,
          taskData.instructions
        ]);

        console.log('✅ 工作流执行结果已保存到task表，任务ID:', insertResult.insertId);
      }
    } catch (saveError) {
      console.error('❌ 保存工作流执行结果到task表失败:', saveError);
      // 不影响主流程，继续返回结果
    }

    res.json(processedResponse);
  } catch (error: any) {
    console.error('❌ 工作流执行失败:', error.message);

    if (error.response) {
      console.error('❌ Coze API 错误响应:', error.response.data);
      res.status(error.response.status).json(error.response.data);
    } else {
      res.status(500).json({
        error: '工作流执行失败',
        message: error.message
      });
    }
  }
});

// 获取工作流执行状态接口
router.get('/workflow/run/:runId', async (req, res) => {
  console.log('📤 收到获取工作流状态请求');
  console.log('运行ID:', req.params.runId);

  try {
    const { runId } = req.params;

    if (!runId) {
      return res.status(400).json({
        error: '参数错误',
        message: 'runId 是必填参数'
      });
    }

    // 获取访问令牌
    let accessToken = await getOAuthAccessToken();
    if (!accessToken) {
      accessToken = await getPersonalAccessToken();
    }

    if (!accessToken) {
      return res.status(401).json({
        error: '认证失败',
        message: '无法获取有效的访问令牌'
      });
    }

    // 调用扣子工作流状态API
    const response = await axios.get(`${COZE_WORKFLOW_API_BASE_URL}/workflow/run/${runId}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('✅ 工作流状态获取成功');

    // 🖼️ 处理工作流状态响应中的图片链接
    let processedResponse = { ...response.data };

    if (processedResponse.data) {
      const checkAndProcessImageUrl = (content: any): any => {
        if (typeof content === 'string') {
          // 检查是否是图片链接
          const imageUrlPattern = /^https?:\/\/.*\.(png|jpg|jpeg|gif|webp)(\?.*)?$/i;
          if (imageUrlPattern.test(content.trim())) {
            console.log('🖼️ 检测到工作流状态返回图片链接，转换为Markdown格式:', content.trim());
            return `![图片](${content.trim()})`;
          }
        } else if (typeof content === 'object' && content !== null) {
          // 递归处理对象中的所有字符串值
          const processed = { ...content };
          for (const key in processed) {
            processed[key] = checkAndProcessImageUrl(processed[key]);
          }
          return processed;
        }
        return content;
      };

      processedResponse.data = checkAndProcessImageUrl(processedResponse.data);
    }

    res.json(processedResponse);
  } catch (error: any) {
    console.error('❌ 获取工作流状态失败:', error.message);

    if (error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      res.status(500).json({
        error: '获取工作流状态失败',
        message: error.message
      });
    }
  }
});

// 获取工作流执行历史和结果接口
router.get('/workflow/execution/:executeId', async (req, res) => {
  console.log('📤 收到获取工作流执行结果请求');
  console.log('执行ID:', req.params.executeId);

  try {
    const { executeId } = req.params;

    if (!executeId) {
      return res.status(400).json({
        error: '参数错误',
        message: 'executeId 是必填参数'
      });
    }

    // 获取访问令牌
    let accessToken = await getOAuthAccessToken();
    if (!accessToken) {
      accessToken = await getPersonalAccessToken();
    }

    if (!accessToken) {
      return res.status(401).json({
        error: '认证失败',
        message: '无法获取有效的访问令牌'
      });
    }

    // 调用扣子获取节点执行历史响应API
    const response = await axios.get(`${COZE_WORKFLOW_API_BASE_URL}/workflow/run/node_execute_histories/${executeId}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('✅ 获取工作流执行结果成功');
    console.log('📥 Coze工作流执行结果API 响应数据:', JSON.stringify(response.data, null, 2));

    // 处理响应数据，提取最终结果
    const responseData = response.data;
    if (responseData && responseData.code === 0 && responseData.data) {
      const histories = responseData.data;

      // 查找最后一个成功执行的节点或输出节点
      let finalResult = null;
      let status = 'running';

      if (Array.isArray(histories)) {
        // 查找输出节点或最后执行的节点
        const outputNode = histories.find(h => h.node_type === 'output' || h.node_type === 'end');
        const lastNode = histories[histories.length - 1];

        if (outputNode && outputNode.status === 'success') {
          finalResult = outputNode.outputs || outputNode.result;
          status = 'success';
        } else if (lastNode) {
          if (lastNode.status === 'success') {
            finalResult = lastNode.outputs || lastNode.result;
            status = 'success';
          } else if (lastNode.status === 'failed') {
            status = 'failed';
            finalResult = lastNode.error_message || '工作流执行失败';
          }
        }
      }

      // 🖼️ 处理最终结果中的图片链接
      const checkAndProcessImageUrl = (content: any): any => {
        if (typeof content === 'string') {
          // 检查是否是图片链接
          const imageUrlPattern = /^https?:\/\/.*\.(png|jpg|jpeg|gif|webp)(\?.*)?$/i;
          if (imageUrlPattern.test(content.trim())) {
            console.log('🖼️ 检测到工作流执行结果图片链接，转换为Markdown格式:', content.trim());
            return `![图片](${content.trim()})`;
          }
        } else if (typeof content === 'object' && content !== null) {
          // 递归处理对象中的所有字符串值
          const processed = { ...content };
          for (const key in processed) {
            processed[key] = checkAndProcessImageUrl(processed[key]);
          }
          return processed;
        }
        return content;
      };

      const processedResult = checkAndProcessImageUrl(finalResult);

      res.json({
        code: 0,
        data: {
          status: status,
          output: processedResult,
          result: processedResult,
          execute_id: executeId,
          histories: histories
        }
      });
    } else {
      res.json(responseData);
    }
  } catch (error: any) {
    console.error('❌ 获取工作流执行结果失败:', error.message);

    if (error.response) {
      console.error('❌ Coze API 错误响应:', error.response.data);
      res.status(error.response.status).json(error.response.data);
    } else {
      res.status(500).json({
        error: '获取工作流执行结果失败',
        message: error.message
      });
    }
  }
});

// 获取工作流列表接口
router.get('/workflows', async (req, res) => {
  console.log('📤 收到获取工作流列表请求');

  try {
    // 获取访问令牌
    let accessToken = await getOAuthAccessToken();
    if (!accessToken) {
      accessToken = await getPersonalAccessToken();
    }

    if (!accessToken) {
      return res.status(401).json({
        error: '认证失败',
        message: '无法获取有效的访问令牌'
      });
    }

    // 调用扣子工作流列表API - 使用正确的API端点
    const response = await axios.get(`${COZE_WORKFLOW_API_BASE_URL}/workflow/list`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      params: {
        page_size: 50,  // 每页数量
        page_num: 1     // 页码
      },
      timeout: 10000
    });

    console.log('✅ 工作流列表获取成功');
    res.json(response.data);
  } catch (error: any) {
    console.error('❌ 获取工作流列表失败:', error.message);

    if (error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      res.status(500).json({
        error: '获取工作流列表失败',
        message: error.message
      });
    }
  }
});

export default router;