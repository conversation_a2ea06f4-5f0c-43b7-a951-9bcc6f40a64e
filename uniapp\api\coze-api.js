// coze-api.js - uniapp版本
// 基于src/api/coze-api.ts的适配版本

// 消息角色常量
export const COZE_MESSAGE_ROLES = {
  USER: 'user',
  ASSISTANT: 'assistant'
};

// 认证方式枚举
export const CozeAuthMode = {
  BASIC: 'basic',  // 基本认证（client_id + client_secret）
  JWT: 'jwt'       // JWT认证（私钥签名）
};

// 从本地存储获取Coze配置
function getCozeConfig() {
  try {
    const config = uni.getStorageSync('coze_config');
    if (config) {
      return JSON.parse(config);
    }
  } catch (error) {
    console.error('获取Coze配置失败:', error);
  }
  return null;
}

// 保存Coze配置到本地存储
function saveCozeConfig(config) {
  try {
    uni.setStorageSync('coze_config', JSON.stringify(config));
  } catch (error) {
    console.error('保存Coze配置失败:', error);
  }
}

import { API_BASE_URL } from '../config/index.js'
import { userStore } from './members.js'

// 获取API基础URL
function getApiBaseUrl() {
  // 使用uniapp的环境变量或配置
  const ENV_CONFIG = uni.getStorageSync('ENV_CONFIG') || {};
  if (ENV_CONFIG.API_BASE_URL) {
    return ENV_CONFIG.API_BASE_URL;
  }

  // 使用配置文件中的默认值
  return API_BASE_URL;
}

// 扣子API客户端类
class CozeApiClient {
  constructor() {
    this.accessToken = null;
    this.tokenExpiry = 0;
    this.clientId = null;
    this.clientSecret = null;
    this.authMode = CozeAuthMode.BASIC;
    this.initialized = false;
    
    // 尝试从本地存储恢复令牌和凭据
    const config = getCozeConfig();
    if (config) {
      this.accessToken = config.accessToken || null;
      this.tokenExpiry = config.tokenExpiry || 0;
      this.clientId = config.clientId || null;
      this.clientSecret = config.clientSecret || null;
      this.authMode = config.authMode || CozeAuthMode.BASIC;
    }
    
    // 初始化时尝试从系统配置加载
    this.loadFromSystemConfig();
  }
  
  // 从系统配置加载凭据
  async loadFromSystemConfig() {
    try {
      const response = await this.request({
        url: '/api/configs/kouzi',
        method: 'GET'
      });

      console.log('Coze配置API完整响应:', response);
      console.log('响应状态码:', response.statusCode);
      console.log('响应数据:', response.data);
      console.log('响应数据类型:', typeof response.data);

      if (response && response.statusCode === 200 && response.data) {
        let configValue = {};

        // 处理后端返回的配置数据格式
        console.log('开始解析配置数据...');
        console.log('response.data.code:', response.data.code);
        console.log('response.data.data:', response.data.data);

        if (response.data.code === 200 && response.data.data && response.data.data.configValue) {
          // 标准格式: { code: 200, data: { configValue: {...} } }
          console.log('使用标准configValue格式');
          configValue = response.data.data.configValue;
        } else {
          console.error('未识别的配置数据格式:', response.data);
          console.error('期望格式: { code: 200, data: { configValue: {...} } }');
          return;
        }

        console.log('Coze配置数据:', configValue);
        console.log('配置项数量:', Object.keys(configValue).length);
        console.log('所有配置项的key:', Object.keys(configValue));

        // 从配置对象中提取值
        let appId = configValue.app_id || configValue.appId || configValue.clientId || '';
        let privateKey = configValue.private_key || configValue.privateKey || '';
        let authMode = (configValue.auth_mode === 'jwt' || configValue.authMode === 'jwt') ? CozeAuthMode.JWT : CozeAuthMode.BASIC;
        let enabled = configValue.enabled === true || configValue.enabled === 'true';

        console.log('提取的配置值:');
        console.log('- appId:', appId ? '***已设置***' : '未设置');
        console.log('- private_key:', privateKey ? '***已设置***' : '未设置');
        console.log('- auth_mode:', authMode);
        console.log('- enabled:', enabled);

        console.log('最终配置:', { appId: appId ? '已设置' : '未设置', privateKey: privateKey ? '已设置' : '未设置', authMode, enabled });

        // 检查配置有效性 - JWT认证需要appId和私钥
        const isValidConfig = appId && (authMode === CozeAuthMode.JWT ? privateKey : true) && enabled;
        console.log('配置有效性检查:', {
          hasAppId: !!appId,
          needsPrivateKey: authMode === CozeAuthMode.JWT,
          hasPrivateKey: !!privateKey,
          isEnabled: enabled,
          isValid: isValidConfig
        });

        // 如果配置有效，设置凭据并获取OAuth令牌
        if (isValidConfig) {
          // 设置基本凭据
          this.setCredentials(appId, privateKey, authMode);

          // 尝试获取OAuth访问令牌
          try {
            console.log('开始获取OAuth访问令牌...');
            await this.getToken();
            this.initialized = true;
            console.log('Coze API初始化成功 - OAuth认证完成');
          } catch (tokenError) {
            console.error('获取OAuth令牌失败:', tokenError);
            // 即使令牌获取失败，也标记为已初始化，因为凭据已设置
            this.initialized = true;
            console.log('Coze API基本初始化完成，但OAuth令牌获取失败');
          }
        } else {
          console.log('Coze API初始化失败，缺少必要配置');
          console.log('失败原因分析:');
          if (!appId) console.log('- 缺少appId');
          if (authMode === CozeAuthMode.JWT && !privateKey) console.log('- JWT模式缺少私钥');
          if (!enabled) console.log('- 配置未启用');
        }
      } else {
        console.log('配置API响应无效或为空');
      }
    } catch (error) {
      console.error('从系统配置加载Coze凭据失败:', error);
    }
  }
  
  // 设置API凭据
  setCredentials(clientId, clientSecret, authMode = CozeAuthMode.BASIC) {
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.authMode = authMode;
    
    // 保存凭据到本地存储
    saveCozeConfig({
      clientId,
      clientSecret,
      authMode,
      accessToken: this.accessToken,
      tokenExpiry: this.tokenExpiry
    });
    
    this.initialized = true;
  }
  
  // 封装uni.request为Promise
  request(options) {
    return new Promise((resolve, reject) => {
      // 构建完整的URL
      let fullUrl = options.url;
      if (!fullUrl.startsWith('http')) {
        fullUrl = API_BASE_URL + fullUrl;
      }

      // 设置默认超时时间
      const defaultTimeout = options.url.includes('/chat') ? 180000 :
                            options.url.includes('/workflow') ? 180000 : 60000; // 聊天和工作流请求3分钟，其他1分钟

      uni.request({
        ...options,
        url: fullUrl,
        timeout: options.timeout || defaultTimeout,
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          console.error('请求失败:', err);
          reject(err);
        }
      });
    });
  }
  
  // 获取OAuth令牌
  async getToken(clientId, clientSecret, authMode) {
    // 如果提供了新的凭据，更新存储的凭据
    if (clientId && clientSecret) {
      this.setCredentials(clientId, clientSecret, authMode || this.authMode);
    }
    
    // 检查是否已有有效令牌
    if (this.accessToken && this.tokenExpiry > Date.now()) {
      return this.accessToken;
    }
    
    // 如果未初始化，尝试从系统配置加载
    if (!this.initialized) {
      await this.loadFromSystemConfig();
    }
    
    // 检查是否有存储的凭据
    if (!this.clientId) {
      throw new Error('未设置Coze API凭据，请先调用setCredentials方法或确保系统配置中有有效的Coze配置');
    }
    
    try {
      // JWT OAuth认证 - 后端自动处理，不需要传递参数
      const response = await this.request({
        url: '/api/proxy/oauth/token',
        method: 'POST',
        data: {}, // 空请求体，后端自动处理JWT认证
        header: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.statusCode === 200 && response.data) {
        const { access_token, expires_in } = response.data;
        this.accessToken = access_token;
        // 设置过期时间（提前5分钟过期，确保有足够时间刷新）
        this.tokenExpiry = Date.now() + (expires_in - 300) * 1000;
        
        // 保存令牌到本地存储
        saveCozeConfig({
          clientId: this.clientId,
          clientSecret: this.clientSecret,
          authMode: this.authMode,
          accessToken: this.accessToken,
          tokenExpiry: this.tokenExpiry
        });
        
        return this.accessToken;
      } else {
        throw new Error(`获取令牌失败: ${response.statusCode} ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.error('获取Coze OAuth令牌失败:', error);
      throw new Error('获取Coze OAuth令牌失败');
    }
  }
  
  // 确保请求头中包含有效的授权令牌
  async ensureToken() {
    // 获取有效令牌
    const token = await this.getToken();
    return `Bearer ${token}`;
  }
  
  // 获取智能体列表
  async getBots() {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();
      
      const response = await this.request({
        url: '/api/proxy/bots',
        method: 'GET',
        header: {
          'Authorization': authorization
        }
      });
      
      if (response.statusCode === 200) {
        return response.data;
      } else {
        throw new Error(`获取智能体列表失败: ${response.statusCode} ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.error('获取Coze智能体列表失败:', error);
      throw new Error('获取Coze智能体列表失败');
    }
  }
  
  // 发送聊天请求 - 支持扣子API v3格式
  async chat(botId, messages, options = {}) {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();

      // 设置默认选项
      const {
        userId = 'default_user',
        stream = false,
        conversationId
      } = options;

      // 构建符合v3 API格式的请求体
      const requestData = {
        bot_id: botId,
        user_id: userId,
        stream: stream,
        messages: messages
      };

      // 如果提供了conversation_id，则添加到请求体中
      if (conversationId) {
        requestData.conversation_id = conversationId;
      }

      console.log('发送聊天请求:', requestData);

      // 获取用户token
      let userToken = null;
      try {
        userToken = userStore.getToken();
        console.log('🔍 获取用户token结果:', {
          hasToken: !!userToken,
          tokenType: typeof userToken,
          tokenLength: userToken ? userToken.length : 0
        });
      } catch (error) {
        console.error('❌ 获取用户token失败:', error);
      }

      // 构建请求头
      const headers = {
        'Authorization': authorization, // Coze API token
        'Content-Type': 'application/json'
      }

      // 如果有用户token，添加用户认证头
      if (userToken && typeof userToken === 'string' && userToken.trim()) {
        headers['X-User-Token'] = `Bearer ${userToken}`
        console.log('🔐 添加用户认证token到聊天请求')
      } else {
        console.log('⚠️ 未找到有效用户token，聊天请求可能失败')
      }

      const response = await this.request({
        url: '/api/proxy/chat',
        method: 'POST',
        data: requestData,
        header: headers
      });

      if (response.statusCode === 200) {
        return response.data;
      } else {
        // 处理不同的HTTP状态码
        let errorMessage = '聊天请求失败';

        if (response.statusCode === 429) {
          // 请求过于频繁
          const errorData = response.data || {};
          errorMessage = errorData.message || errorData.error || '请求过于频繁，请稍后再试';
        } else if (response.statusCode === 401) {
          errorMessage = '认证失败，请重新登录';
        } else if (response.statusCode === 500) {
          errorMessage = '服务器内部错误，请稍后重试';
        } else {
          errorMessage = `请求失败 (${response.statusCode})`;
          if (response.data && response.data.message) {
            errorMessage += ': ' + response.data.message;
          }
        }

        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Coze聊天请求失败:', error);
      throw new Error('Coze聊天请求失败');
    }
  }
  
  // 简化的聊天方法 - 发送单条消息
  async sendMessage(botId, message, userId = 'default_user') {
    const messages = [
      {
        role: COZE_MESSAGE_ROLES.USER,
        content: message,
        content_type: 'text'
      }
    ];

    return await this.chat(botId, messages, { userId });
  }

  // 执行工作流 - 根据扣子官方API文档实现
  async runWorkflow(workflowId, parameters = {}, options = {}) {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();

      // 设置默认选项
      const {
        userId = 'default_user',
        stream = false,
        consumption = 10000000 // 默认消耗点数
      } = options;

      console.log('📊 前端coze-api.js 接收到的参数:', {
        workflowId,
        parameters,
        options,
        consumption
      });

      // 构建符合扣子工作流API格式的请求体
      const requestData = {
        workflow_id: workflowId,
        parameters: parameters,
        user_id: userId,
        stream: stream,
        consumption: consumption // 添加消耗点数字段
      };

      console.log('🚀 发送工作流执行请求:', requestData);

      // 获取用户token
      let userToken = null;
      try {
        userToken = userStore.getToken();
        console.log('🔍 获取用户token结果:', {
          hasToken: !!userToken,
          tokenType: typeof userToken,
          tokenLength: userToken ? userToken.length : 0
        });
      } catch (error) {
        console.error('❌ 获取用户token失败:', error);
      }

      // 构建请求头
      const headers = {
        'Authorization': authorization, // Coze API token
        'Content-Type': 'application/json'
      }

      console.log('🔍 请求详细信息:', {
        url: '/api/proxy/workflow/run',
        method: 'POST',
        headers: headers,
        timestamp: new Date().toISOString()
      });

      // 如果有用户token，添加用户认证头
      if (userToken && typeof userToken === 'string' && userToken.trim()) {
        headers['X-User-Token'] = `Bearer ${userToken}`
        console.log('🔐 添加用户认证token到工作流请求')
      } else {
        console.log('⚠️ 未找到有效用户token，工作流请求可能失败')
      }

      console.log('📡 开始发送uni.request...');
      const response = await this.request({
        url: '/api/proxy/workflow/run',
        method: 'POST',
        data: requestData,
        header: headers
      });

      console.log('📥 uni.request响应:', {
        statusCode: response.statusCode,
        dataType: typeof response.data,
        hasData: !!response.data,
        headerKeys: response.header ? Object.keys(response.header) : []
      });
      console.log('📊 完整响应数据:', response);

      if (response.statusCode === 200) {
        console.log('✅ 工作流请求成功，返回数据');
        return response.data;
      } else {
        console.error('❌ 工作流请求失败:', response.statusCode, response.data);
        throw new Error(`工作流执行失败: ${response.statusCode} ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.error('❌ Coze工作流执行失败:', error);
      console.error('🔍 错误详细分析:', {
        message: error.message,
        name: error.name,
        stack: error.stack,
        isNetworkError: error.message.includes('网络') || error.message.includes('timeout'),
        timestamp: new Date().toISOString()
      });
      throw new Error(`Coze工作流执行失败: ${error.message}`);
    }
  }

  // 获取工作流执行状态
  async getWorkflowRunStatus(runId) {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();

      // 获取用户token
      let userToken = null;
      try {
        userToken = userStore.getToken();
      } catch (error) {
        console.error('❌ 获取用户token失败:', error);
      }

      // 构建请求头
      const headers = {
        'Authorization': authorization
      }

      // 如果有用户token，添加用户认证头
      if (userToken && typeof userToken === 'string' && userToken.trim()) {
        headers['X-User-Token'] = `Bearer ${userToken}`
      }

      const response = await this.request({
        url: `/api/proxy/workflow/run/${runId}`,
        method: 'GET',
        header: headers
      });

      if (response.statusCode === 200) {
        return response.data;
      } else {
        throw new Error(`获取工作流状态失败: ${response.statusCode} ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.error('获取Coze工作流状态失败:', error);
      throw new Error('获取Coze工作流状态失败');
    }
  }

  // 获取工作流执行结果
  async getWorkflowExecutionResult(executeId) {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();

      // 获取用户token
      let userToken = null;
      try {
        userToken = userStore.getToken();
      } catch (error) {
        console.error('❌ 获取用户token失败:', error);
      }

      // 构建请求头
      const headers = {
        'Authorization': authorization
      }

      // 如果有用户token，添加用户认证头
      if (userToken && typeof userToken === 'string' && userToken.trim()) {
        headers['X-User-Token'] = `Bearer ${userToken}`
      }

      const response = await this.request({
        url: `/api/proxy/workflow/execution/${executeId}`,
        method: 'GET',
        header: headers
      });

      if (response.statusCode === 200) {
        return response.data;
      } else {
        throw new Error(`获取工作流执行结果失败: ${response.statusCode} ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.error('获取Coze工作流执行结果失败:', error);
      throw new Error('获取Coze工作流执行结果失败');
    }
  }

  // 获取工作流列表
  async getWorkflows() {
    try {
      // 确保有有效令牌
      const authorization = await this.ensureToken();

      // 获取用户token
      let userToken = null;
      try {
        userToken = userStore.getToken();
      } catch (error) {
        console.error('❌ 获取用户token失败:', error);
      }

      // 构建请求头
      const headers = {
        'Authorization': authorization
      }

      // 如果有用户token，添加用户认证头
      if (userToken && typeof userToken === 'string' && userToken.trim()) {
        headers['X-User-Token'] = `Bearer ${userToken}`
      }

      const response = await this.request({
        url: '/api/proxy/workflows',
        method: 'GET',
        header: headers
      });

      if (response.statusCode === 200) {
        return response.data;
      } else {
        throw new Error(`获取工作流列表失败: ${response.statusCode} ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.error('获取Coze工作流列表失败:', error);
      throw new Error('获取Coze工作流列表失败');
    }
  }

  // 检查是否已初始化
  isInitialized() {
    // 检查是否有基本凭据和初始化标志
    return this.initialized && this.clientId;
  }
}

// 导出Coze API客户端单例
export const cozeApi = new CozeApiClient();

// 默认导出
export default cozeApi; 